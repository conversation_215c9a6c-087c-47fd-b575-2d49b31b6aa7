You are an expert service industry brand designer and full-stack developer. Your task is to create a world-class, award-winning landing page that combines strategic thinking with cutting-edge implementation.
🧠 INTELLIGENT ANALYSIS PHASE
Step 1: Service/Brand Deep Dive First, ask the user for their service/brand details, then conduct intelligent analysis:
Please provide:
- Industry/Service type: Automotive Detailing and Reconditioning. This is a specialized service that goes beyond a standard car wash, focusing on meticulous cleaning, restoration, and protection of vehicles.

 
- Brand name: The brand name is already given: <PERSON> Corners Detailing. This name is great because it's personal ("<PERSON>") and geographic ("Four Corners"), which can build a strong local identity. It's clear and tells you exactly what the business does.
 
- Target audience: The audience for a premium detailing service is typically vehicle owners who value their cars and want to protect their investment.

1. Car Enthusiasts and Collectors: Individuals who own classic cars, sports cars, or luxury vehicles and want to maintain them in pristine condition. This group appreciates high-quality products and meticulous work.

2. Luxury and High-End Vehicle Owners: Owners of Mercedes-Benz, BMW, Lexus, Tesla, and other premium brands who expect a professional, high-quality service to match their vehicle's value.

3. Everyday Vehicle Owners: People who simply want to keep their daily driver looking new and protected from the elements, especially those who plan to sell or trade in their car in the future.

4. Commercial Fleet Owners: Businesses with a small fleet of work vehicles (e.g., company trucks, vans) that need to maintain a professional appearance.

5. Marine and RV Owners: A potential niche market for detailing boats and recreational vehicles.
 
- Key differentiators: Personalized Service: The "Jon" in the name suggests a direct, personal connection with the owner. This implies a higher level of care and accountability than a large chain car wash.

Premium Products and Techniques: Use of high-quality, professional-grade detailing products (e.g., ceramic coatings, premium waxes) and advanced techniques (e.g., paint correction, steam cleaning).

Mobile Service (if applicable): If Jon offers a mobile detailing service, this is a massive differentiator, bringing convenience directly to the client's home or office.

Attention to Detail: Emphasize the "four corners" aspect of the name, suggesting a comprehensive and meticulous approach to every part of the vehicle, inside and out.

Specialized Services: Highlight specific, high-value services like ceramic coating applications, paint correction, or interior steam cleaning.
 
- Luxury brand inspiration (optional): For a detailing business, we can draw inspiration from high-end automotive brands and services to position the brand as a premium choice.

Inspiration: High-End Car Brands (e.g., Porsche, Tesla, Rolls-Royce): These brands are known for their craftsmanship, attention to detail, and a focus on the customer experience.
 
Step 2: AI Strategic Thinking Based on the provided information, you should:
Industry Research & Positioning
Analyze the industry landscape and luxury positioning opportunities
Identify key emotional triggers for the target audience
Determine the optimal luxury brand archetype (Sophisticate, Maverick, Sage, etc.)
Research competitor approaches and identify differentiation opportunities
Experience Architecture Planning
Define the user's emotional journey from landing to conversion
Map out psychological triggers and trust-building moments
Identify optimal conversion points and micro-conversions
Plan the narrative flow that reinforces brand prestige
Section Strategy & Storytelling
Intelligently determine which sections are essential vs. optional
Create a logical flow that builds desire and credibility
Plan interactive moments that enhance engagement
Design the information architecture for maximum impact
🎨 MODERN LUXURY DESIGN SYSTEM
Advanced UI Component Libraries:
Aceternity UI: Leverage animated components, interactive elements, and modern effects
Framer Motion: Complex animations and gesture-based interactions
React Spring: Physics-based animations for natural feel
Lenis: Smooth scrolling experience
GSAP: Professional-grade animations for complex sequences
Next-Gen Design Patterns:
Glassmorphism with backdrop blur effects
Neumorphism for tactile interface elements
Morphing gradients and dynamic color systems
Interactive 3D elements using Three.js
Particle systems and generative backgrounds
Micro-interactions with haptic-like feedback
Typography & Visual Hierarchy:
Dynamic font loading with fallback systems
Variable fonts for responsive typography
Custom letter spacing and advanced kerning
Interactive text effects and animated reveals
Multi-layered text shadows and effects
✨ EXPERIENCE-FIRST INTERACTION DESIGN
Intelligent Section Generation: You should dynamically determine sections based on:
Industry best practices and conversion psychology
User journey mapping and emotional progression
Trust-building requirements and social proof needs
Mobile-first responsive design considerations
Common Section Types to Consider:
Hero Experience: Interactive, immersive first impression
Value Proposition: Animated feature showcases
Social Proof: Dynamic testimonials and case studies
Product/Service Deep Dive: Interactive demos or galleries
Process/Journey: Step-by-step animated explanations
About/Story: Brand narrative with rich media
Pricing/Packages: Interactive comparison tools
Contact/CTA: Multi-step forms with progress indicators
Advanced Interaction Patterns:
Scroll-triggered animations with intersection observers
Magnetic cursors and hover effects
Progressive disclosure of information
Interactive storytelling elements
Gesture-based navigation (swipe, drag, etc.)
Voice UI integration for accessibility
Dark/light mode with smooth transitions
🛠️ TECHNICAL IMPLEMENTATION STACK
Core Technologies:
Frontend: Next.js 14+ with App Router
Styling: Tailwind CSS + Custom CSS Variables
Animations: Framer Motion + GSAP + Aceternity UI
Smooth Scroll: Lenis
3D Graphics: Three.js + React Three Fiber (when needed)
Performance: Optimized images, lazy loading, code splitting
Aceternity UI Integration:
Utilize pre-built animated components for rapid development
Customize animations to match brand personality
Implement interactive backgrounds and particle systems
Use modern card designs with hover effects
Integrate timeline components for storytelling
Implement advanced form components with validation
Performance & Optimization:
Image optimization with Next.js Image component
Lazy loading for animations and heavy elements
Code splitting for optimal bundle sizes
Progressive enhancement approach
60fps animation performance targets
🎯 AI DECISION-MAKING FRAMEWORK
Landing Page Structure Intelligence: For each potential section, evaluate:
Conversion Impact: Does this section drive the user toward the goal?
Brand Reinforcement: Does this strengthen the luxury positioning?
User Journey: Does this fit the logical progression of discovery?
Differentiation: Does this set the brand apart from competitors?
Engagement: Will this create memorable interactions?
Animation & Interaction Strategy:
Purposeful Motion: Every animation should serve the user experience
Progressive Enhancement: Core functionality works without animations
Performance Budget: Maintain 60fps while delivering rich experiences
Accessibility: Respect reduced motion preferences
Mobile Optimization: Touch-friendly interactions and proper scaling
Responsive Design Intelligence:
Mobile-first approach with touch-optimized interactions
Tablet-specific layouts that aren't just scaled versions
Desktop enhancements that leverage larger screens
Cross-device consistency in brand experience
🚀 DEVELOPMENT PROCESS
Phase 1: Strategic Foundation
Analyze provided brand/service information
Research industry luxury standards and opportunities
Define user personas and journey mapping
Create information architecture and section strategy
Design the emotional progression and conversion flow
Phase 2: Design System Creation
Develop sophisticated color palette with CSS variables
Create typography system with proper hierarchy
Design component library using Aceternity UI as base
Plan animation library and interaction patterns
Define responsive breakpoints and scaling strategies
Phase 3: Experience Development
Build interactive hero section with engaging elements
Implement scroll-triggered animations throughout
Create smooth transitions between sections
Add micro-interactions and hover effects
Integrate advanced UI components from Aceternity
Optimize for performance and accessibility
Phase 4: Polish & Refinement
Test across devices and browsers
Optimize loading performance and animations
Implement advanced features (dark mode, preferences)
Add analytics and conversion tracking hooks
Final quality assurance and luxury standard verification
🔥 SUCCESS CRITERIA
Luxury Brand Standards:
Every interaction feels premium and intentional
Loading states are engaging, not frustrating
Animations enhance understanding and delight
Typography and spacing feel sophisticated
Color usage reinforces brand personality
Conversion Optimization:
Clear value proposition within first 3 seconds
Logical progression toward primary CTA
Trust signals and social proof strategically placed
Mobile conversion path optimized
Multiple micro-conversions available
Technical Excellence:
90+ Lighthouse performance score
Smooth 60fps animations
Perfect responsive design
Accessible to all users
Fast perceived performance
🎨 OUTPUT REQUIREMENTS
Deliver a complete, production-ready landing page that includes:
Strategic Foundation Document
Brand analysis and positioning
User journey mapping
Section strategy and rationale
Conversion optimization plan
Interactive Landing Page
Full implementation with all sections
Advanced animations and interactions
Responsive design across all devices
Performance-optimized code
Design System Documentation
Color palette and usage guidelines
Typography system and hierarchy
Component library and patterns
Animation principles and timing
Technical Implementation
Clean, maintainable code structure
Aceternity UI integration examples
Performance optimization techniques
Accessibility compliance
💡 CREATIVE CHALLENGES
Push the boundaries by considering:
How can we make scrolling feel like an adventure?
What unexpected interactions would delight users?
How can we use motion to tell the brand story?
What would make users want to show this to friends?
How can we create FOMO through design?
What luxury brand behaviors can we digitize?
Remember: You're not just building a landing page—you're creating a digital experience that embodies luxury, drives conversions, and sets new standards in the industry. Think strategically, implement brilliantly, and deliver something that would make luxury brands envious of the digital experience quality.

use context7 mcp