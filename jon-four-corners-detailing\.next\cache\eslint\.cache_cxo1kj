[{"D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\app\\layout.tsx": "1", "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\app\\page.tsx": "2", "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\navigation\\footer.tsx": "3", "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\navigation\\navbar.tsx": "4", "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\sections\\contact.tsx": "5", "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\sections\\gallery.tsx": "6", "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\sections\\hero.tsx": "7", "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\sections\\services.tsx": "8", "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\sections\\testimonials.tsx": "9", "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\ui\\background-gradient.tsx": "10", "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\ui\\card-hover-effect.tsx": "11", "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\ui\\hover-border-gradient.tsx": "12", "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\ui\\text-generate-effect.tsx": "13", "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\lib\\utils.ts": "14"}, {"size": 1806, "mtime": 1751057052293, "results": "15", "hashOfConfig": "16"}, {"size": 705, "mtime": 1751059103233, "results": "17", "hashOfConfig": "16"}, {"size": 9258, "mtime": 1751057387840, "results": "18", "hashOfConfig": "16"}, {"size": 5834, "mtime": 1751059194450, "results": "19", "hashOfConfig": "16"}, {"size": 11721, "mtime": 1751058161447, "results": "20", "hashOfConfig": "16"}, {"size": 9333, "mtime": 1751059089978, "results": "21", "hashOfConfig": "16"}, {"size": 7433, "mtime": 1751059164940, "results": "22", "hashOfConfig": "16"}, {"size": 5770, "mtime": 1751059007161, "results": "23", "hashOfConfig": "16"}, {"size": 6853, "mtime": 1751058138149, "results": "24", "hashOfConfig": "16"}, {"size": 2466, "mtime": 1751056972347, "results": "25", "hashOfConfig": "16"}, {"size": 2949, "mtime": 1751059050412, "results": "26", "hashOfConfig": "16"}, {"size": 3245, "mtime": 1751057002837, "results": "27", "hashOfConfig": "16"}, {"size": 1388, "mtime": 1751056982946, "results": "28", "hashOfConfig": "16"}, {"size": 3624, "mtime": 1751058223631, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1m50m3d", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 2, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\app\\layout.tsx", [], [], "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\app\\page.tsx", [], [], "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\navigation\\footer.tsx", [], [], "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\navigation\\navbar.tsx", [], [], "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\sections\\contact.tsx", ["72", "73"], [], "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\sections\\gallery.tsx", ["74", "75"], [], "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\sections\\hero.tsx", ["76"], [], "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\sections\\services.tsx", [], [], "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\sections\\testimonials.tsx", ["77", "78", "79", "80"], [], "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\ui\\background-gradient.tsx", [], [], "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\ui\\card-hover-effect.tsx", ["81", "82"], [], "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\ui\\hover-border-gradient.tsx", ["83", "84"], [], "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\ui\\text-generate-effect.tsx", ["85", "86"], [], "D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\lib\\utils.ts", [], [], {"ruleId": "87", "severity": 2, "message": "88", "line": 13, "column": 3, "nodeType": null, "messageId": "89", "endLine": 13, "endColumn": 6}, {"ruleId": "90", "severity": 2, "message": "91", "line": 126, "column": 51, "nodeType": "92", "messageId": "93", "suggestions": "94"}, {"ruleId": "87", "severity": 2, "message": "95", "line": 6, "column": 10, "nodeType": null, "messageId": "89", "endLine": 6, "endColumn": 21}, {"ruleId": "87", "severity": 2, "message": "96", "line": 6, "column": 23, "nodeType": null, "messageId": "89", "endLine": 6, "endColumn": 35}, {"ruleId": "90", "severity": 2, "message": "91", "line": 99, "column": 138, "nodeType": "92", "messageId": "93", "suggestions": "97"}, {"ruleId": "90", "severity": 2, "message": "91", "line": 77, "column": 16, "nodeType": "92", "messageId": "93", "suggestions": "98"}, {"ruleId": "90", "severity": 2, "message": "91", "line": 77, "column": 50, "nodeType": "92", "messageId": "93", "suggestions": "99"}, {"ruleId": "90", "severity": 2, "message": "100", "line": 128, "column": 17, "nodeType": "92", "messageId": "93", "suggestions": "101"}, {"ruleId": "90", "severity": 2, "message": "100", "line": 128, "column": 39, "nodeType": "92", "messageId": "93", "suggestions": "102"}, {"ruleId": "103", "severity": 2, "message": "104", "line": 19, "column": 8, "nodeType": "105", "messageId": "106", "endLine": 19, "endColumn": 20, "fix": "107"}, {"ruleId": "103", "severity": 2, "message": "108", "line": 19, "column": 22, "nodeType": "105", "messageId": "106", "endLine": 19, "endColumn": 37, "fix": "109"}, {"ruleId": "110", "severity": 1, "message": "111", "line": 57, "column": 6, "nodeType": "112", "endLine": 57, "endColumn": 15, "suggestions": "113"}, {"ruleId": "87", "severity": 2, "message": "114", "line": 61, "column": 22, "nodeType": null, "messageId": "89", "endLine": 61, "endColumn": 27}, {"ruleId": "103", "severity": 2, "message": "115", "line": 19, "column": 7, "nodeType": "105", "messageId": "106", "endLine": 19, "endColumn": 17, "fix": "116"}, {"ruleId": "110", "severity": 1, "message": "117", "line": 33, "column": 6, "nodeType": "112", "endLine": 33, "endColumn": 21, "suggestions": "118"}, "@typescript-eslint/no-unused-vars", "'Car' is defined but never used.", "unusedVar", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["119", "120", "121", "122"], "'ChevronLeft' is defined but never used.", "'ChevronRight' is defined but never used.", ["123", "124", "125", "126"], ["127", "128", "129", "130"], ["131", "132", "133", "134"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["135", "136", "137", "138"], ["139", "140", "141", "142"], "prefer-const", "'hoveredIndex' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "143", "text": "144"}, "'setHoveredIndex' is never reassigned. Use 'const' instead.", {"range": "145", "text": "144"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'duration' and 'rotateDirection'. Either include them or remove the dependency array.", "ArrayExpression", ["146"], "'event' is defined but never used.", "'wordsArray' is never reassigned. Use 'const' instead.", {"range": "147", "text": "148"}, "React Hook useEffect has missing dependencies: 'animate', 'duration', and 'filter'. Either include them or remove the dependency array. Mutable values like 'scope.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["149"], {"messageId": "150", "data": "151", "fix": "152", "desc": "153"}, {"messageId": "150", "data": "154", "fix": "155", "desc": "156"}, {"messageId": "150", "data": "157", "fix": "158", "desc": "159"}, {"messageId": "150", "data": "160", "fix": "161", "desc": "162"}, {"messageId": "150", "data": "163", "fix": "164", "desc": "153"}, {"messageId": "150", "data": "165", "fix": "166", "desc": "156"}, {"messageId": "150", "data": "167", "fix": "168", "desc": "159"}, {"messageId": "150", "data": "169", "fix": "170", "desc": "162"}, {"messageId": "150", "data": "171", "fix": "172", "desc": "153"}, {"messageId": "150", "data": "173", "fix": "174", "desc": "156"}, {"messageId": "150", "data": "175", "fix": "176", "desc": "159"}, {"messageId": "150", "data": "177", "fix": "178", "desc": "162"}, {"messageId": "150", "data": "179", "fix": "180", "desc": "153"}, {"messageId": "150", "data": "181", "fix": "182", "desc": "156"}, {"messageId": "150", "data": "183", "fix": "184", "desc": "159"}, {"messageId": "150", "data": "185", "fix": "186", "desc": "162"}, {"messageId": "150", "data": "187", "fix": "188", "desc": "189"}, {"messageId": "150", "data": "190", "fix": "191", "desc": "192"}, {"messageId": "150", "data": "193", "fix": "194", "desc": "195"}, {"messageId": "150", "data": "196", "fix": "197", "desc": "198"}, {"messageId": "150", "data": "199", "fix": "200", "desc": "189"}, {"messageId": "150", "data": "201", "fix": "202", "desc": "192"}, {"messageId": "150", "data": "203", "fix": "204", "desc": "195"}, {"messageId": "150", "data": "205", "fix": "206", "desc": "198"}, [338, 406], "const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);", [338, 406], {"desc": "207", "fix": "208"}, [375, 409], "const wordsArray = words.split(\" \");", {"desc": "209", "fix": "210"}, "replaceWithAlt", {"alt": "211"}, {"range": "212", "text": "213"}, "Replace with `&apos;`.", {"alt": "214"}, {"range": "215", "text": "216"}, "Replace with `&lsquo;`.", {"alt": "217"}, {"range": "218", "text": "219"}, "Replace with `&#39;`.", {"alt": "220"}, {"range": "221", "text": "222"}, "Replace with `&rsquo;`.", {"alt": "211"}, {"range": "223", "text": "224"}, {"alt": "214"}, {"range": "225", "text": "226"}, {"alt": "217"}, {"range": "227", "text": "228"}, {"alt": "220"}, {"range": "229", "text": "230"}, {"alt": "211"}, {"range": "231", "text": "232"}, {"alt": "214"}, {"range": "233", "text": "234"}, {"alt": "217"}, {"range": "235", "text": "236"}, {"alt": "220"}, {"range": "237", "text": "238"}, {"alt": "211"}, {"range": "239", "text": "240"}, {"alt": "214"}, {"range": "241", "text": "242"}, {"alt": "217"}, {"range": "243", "text": "244"}, {"alt": "220"}, {"range": "245", "text": "246"}, {"alt": "247"}, {"range": "248", "text": "249"}, "Replace with `&quot;`.", {"alt": "250"}, {"range": "251", "text": "252"}, "Replace with `&ldquo;`.", {"alt": "253"}, {"range": "254", "text": "255"}, "Replace with `&#34;`.", {"alt": "256"}, {"range": "257", "text": "258"}, "Replace with `&rdquo;`.", {"alt": "247"}, {"range": "259", "text": "260"}, {"alt": "250"}, {"range": "261", "text": "262"}, {"alt": "253"}, {"range": "263", "text": "264"}, {"alt": "256"}, {"range": "265", "text": "266"}, "Update the dependencies array to be: [duration, hovered, rotateDirection]", {"range": "267", "text": "268"}, "Update the dependencies array to be: [animate, duration, filter]", {"range": "269", "text": "270"}, "&apos;", [3866, 3895], "We&apos;ll respond within 24 hours", "&lsquo;", [3866, 3895], "We&lsquo;ll respond within 24 hours", "&#39;", [3866, 3895], "We&#39;ll respond within 24 hours", "&rsquo;", [3866, 3895], "We&rsquo;ll respond within 24 hours", [5209, 5295], "Faded paint, water spots, and surface contamination diminish your vehicle&apos;s appearance", [5209, 5295], "Faded paint, water spots, and surface contamination diminish your vehicle&lsquo;s appearance", [5209, 5295], "Faded paint, water spots, and surface contamination diminish your vehicle&#39;s appearance", [5209, 5295], "Faded paint, water spots, and surface contamination diminish your vehicle&rsquo;s appearance", [2961, 3130], "\n            Don&apos;t just take our word for it. Here's what vehicle owners across the Four Corners region \n            say about our premium detailing services.\n          ", [2961, 3130], "\n            Don&lsquo;t just take our word for it. Here's what vehicle owners across the Four Corners region \n            say about our premium detailing services.\n          ", [2961, 3130], "\n            Don&#39;t just take our word for it. Here's what vehicle owners across the Four Corners region \n            say about our premium detailing services.\n          ", [2961, 3130], "\n            Don&rsquo;t just take our word for it. Here's what vehicle owners across the Four Corners region \n            say about our premium detailing services.\n          ", [2961, 3130], "\n            Don't just take our word for it. Here&apos;s what vehicle owners across the Four Corners region \n            say about our premium detailing services.\n          ", [2961, 3130], "\n            Don't just take our word for it. Here&lsquo;s what vehicle owners across the Four Corners region \n            say about our premium detailing services.\n          ", [2961, 3130], "\n            Don't just take our word for it. Here&#39;s what vehicle owners across the Four Corners region \n            say about our premium detailing services.\n          ", [2961, 3130], "\n            Don't just take our word for it. Here&rsquo;s what vehicle owners across the Four Corners region \n            say about our premium detailing services.\n          ", "&quot;", [4919, 4937], "\n                &quot;", "&ldquo;", [4919, 4937], "\n                &ldquo;", "&#34;", [4919, 4937], "\n                &#34;", "&rdquo;", [4919, 4937], "\n                &rdquo;", [4958, 4974], "&quot;\n              ", [4958, 4974], "&ldquo;\n              ", [4958, 4974], "&#34;\n              ", [4958, 4974], "&rdquo;\n              ", [1919, 1928], "[duration, hovered, rotateDirection]", [644, 659], "[animate, duration, filter]"]