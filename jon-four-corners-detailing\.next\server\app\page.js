(()=>{var e={};e.id=974,e.ids=[974],e.modules={63:(e,t,r)=>{Promise.resolve().then(r.bind(r,1350)),Promise.resolve().then(r.bind(r,1352)),Promise.resolve().then(r.bind(r,2750)),Promise.resolve().then(r.bind(r,4862)),Promise.resolve().then(r.bind(r,4832)),Promise.resolve().then(r.bind(r,7483)),Promise.resolve().then(r.bind(r,3677))},331:(e,t,r)=>{"use strict";r.d(t,{P:()=>rA});var i,n,s=r(6570),a=r(4693),o=r(1565);function l(e,t,r={}){let i=(0,a.K)(e,t,"exit"===r.type?e.presenceContext?.custom:void 0),{transition:n=e.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(n=r.transitionOverride);let s=i?()=>Promise.all((0,o.$)(e,i,r)):()=>Promise.resolve(),c=e.variantChildren&&e.variantChildren.size?(i=0)=>{let{delayChildren:s=0,staggerChildren:a,staggerDirection:o}=n;return function(e,t,r=0,i=0,n=1,s){let a=[],o=(e.variantChildren.size-1)*i,c=1===n?(e=0)=>e*i:(e=0)=>o-e*i;return Array.from(e.variantChildren).sort(u).forEach((e,i)=>{e.notify("AnimationStart",t),a.push(l(e,t,{...s,delay:r+c(i)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,s+i,a,o,r)}:()=>Promise.resolve(),{when:d}=n;if(!d)return Promise.all([s(),c(r.delay)]);{let[e,t]="beforeChildren"===d?[s,c]:[c,s];return e().then(()=>t())}}function u(e,t){return e.sortNodePosition(t)}var c=r(7292);function d(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let i=0;i<r;i++)if(t[i]!==e[i])return!1;return!0}var h=r(567),p=r(1328);let m=p._.length,f=[...p.U].reverse(),g=p.U.length;function x(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function v(){return{animate:x(!0),whileInView:x(),whileHover:x(),whileTap:x(),whileDrag:x(),whileFocus:x(),exit:x()}}class y{constructor(e){this.isMounted=!1,this.node=e}update(){}}class b extends y{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:r})=>(function(e,t,r={}){let i;if(e.notify("AnimationStart",t),Array.isArray(t))i=Promise.all(t.map(t=>l(e,t,r)));else if("string"==typeof t)i=l(e,t,r);else{let n="function"==typeof t?(0,a.K)(e,t,r.custom):t;i=Promise.all((0,o.$)(e,n,r))}return i.then(()=>{e.notify("AnimationComplete",t)})})(e,t,r))),r=v(),i=!0,n=t=>(r,i)=>{let n=(0,a.K)(e,i,"exit"===t?e.presenceContext?.custom:void 0);if(n){let{transition:e,transitionEnd:t,...i}=n;r={...r,...i,...t}}return r};function u(o){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let r=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(r.initial=t.props.initial),r}let r={};for(let e=0;e<m;e++){let i=p._[e],n=t.props[i];((0,h.w)(n)||!1===n)&&(r[i]=n)}return r}(e.parent)||{},x=[],v=new Set,y={},b=1/0;for(let t=0;t<g;t++){var w,j;let a=f[t],p=r[a],m=void 0!==l[a]?l[a]:u[a],g=(0,h.w)(m),P=a===o?p.isActive:null;!1===P&&(b=t);let k=m===u[a]&&m!==l[a]&&g;if(k&&i&&e.manuallyAnimateOnMount&&(k=!1),p.protectedKeys={...y},!p.isActive&&null===P||!m&&!p.prevProp||(0,s.N)(m)||"boolean"==typeof m)continue;let A=(w=p.prevProp,"string"==typeof(j=m)?j!==w:!!Array.isArray(j)&&!d(j,w)),T=A||a===o&&p.isActive&&!k&&g||t>b&&g,E=!1,N=Array.isArray(m)?m:[m],S=N.reduce(n(a),{});!1===P&&(S={});let{prevResolvedValues:C={}}=p,M={...C,...S},R=t=>{T=!0,v.has(t)&&(E=!0,v.delete(t)),p.needsAnimating[t]=!0;let r=e.getValue(t);r&&(r.liveStyle=!1)};for(let e in M){let t=S[e],r=C[e];if(y.hasOwnProperty(e))continue;let i=!1;((0,c.p)(t)&&(0,c.p)(r)?d(t,r):t===r)?void 0!==t&&v.has(e)?R(e):p.protectedKeys[e]=!0:null!=t?R(e):v.add(e)}p.prevProp=m,p.prevResolvedValues=S,p.isActive&&(y={...y,...S}),i&&e.blockInitialAnimation&&(T=!1);let D=!(k&&A)||E;T&&D&&x.push(...N.map(e=>({animation:e,options:{type:a}})))}if(v.size){let t={};if("boolean"!=typeof l.initial){let r=(0,a.K)(e,Array.isArray(l.initial)?l.initial[0]:l.initial);r&&r.transition&&(t.transition=r.transition)}v.forEach(r=>{let i=e.getBaseTarget(r),n=e.getValue(r);n&&(n.liveStyle=!0),t[r]=i??null}),x.push({animation:t})}let P=!!x.length;return i&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(P=!1),i=!1,P?t(x):Promise.resolve()}return{animateChanges:u,setActive:function(t,i){if(r[t].isActive===i)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,i)),r[t].isActive=i;let n=u(t);for(let e in r)r[e].protectedKeys={};return n},setAnimateFunction:function(r){t=r(e)},getState:()=>r,reset:()=>{r=v(),i=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();(0,s.N)(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let w=0;class j extends y{constructor(){super(...arguments),this.id=w++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;let i=this.node.animationState.setActive("exit",!e);t&&!e&&i.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}var P=r(3361);let k={x:!1,y:!1};var A=r(2874),T=r(3671),E=r(8028),N=r(6244),S=r(7238);function C(e,t,r,i={passive:!0}){return e.addEventListener(t,r,i),()=>e.removeEventListener(t,r)}let M=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function R(e){return{point:{x:e.pageX,y:e.pageY}}}let D=e=>t=>M(t)&&e(t,R(t));function V(e,t,r,i){return C(e,t,D(r),i)}var O=r(2572);function _(e){return e.max-e.min}function F(e,t,r,i=.5){e.origin=i,e.originPoint=(0,E.k)(t.min,t.max,e.origin),e.scale=_(r)/_(t),e.translate=(0,E.k)(r.min,r.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function L(e,t,r,i){F(e.x,t.x,r.x,i?i.originX:void 0),F(e.y,t.y,r.y,i?i.originY:void 0)}function I(e,t,r){e.min=r.min+t.min,e.max=e.min+_(t)}function z(e,t,r){e.min=t.min-r.min,e.max=e.min+_(t)}function $(e,t,r){z(e.x,t.x,r.x),z(e.y,t.y,r.y)}var B=r(4538);function U(e){return[e("x"),e("y")]}var W=r(2953);let q=({current:e})=>e?e.ownerDocument.defaultView:null;function X(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}var G=r(7283),H=r(8205),K=r(7211);let Y=(e,t)=>Math.abs(e-t);class Q{constructor(e,t,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=ee(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){return Math.sqrt(Y(e.x,t.x)**2+Y(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!r)return;let{point:i}=e,{timestamp:n}=T.uv;this.history.push({...i,timestamp:n});let{onStart:s,onMove:a}=this.handlers;t||(s&&s(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=J(t,this.transformPagePoint),T.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:r,onSessionEnd:i,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=ee("pointercancel"===e.type?this.lastMoveEventInfo:J(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,s),i&&i(e,s)},!M(e))return;this.dragSnapToOrigin=n,this.handlers=t,this.transformPagePoint=r,this.contextWindow=i||window;let s=J(R(e),this.transformPagePoint),{point:a}=s,{timestamp:o}=T.uv;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=t;l&&l(e,ee(s,this.history)),this.removeListeners=(0,H.F)(V(this.contextWindow,"pointermove",this.handlePointerMove),V(this.contextWindow,"pointerup",this.handlePointerUp),V(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),(0,T.WG)(this.updatePoint)}}function J(e,t){return t?{point:t(e.point)}:e}function Z(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ee({point:e},t){return{point:e,delta:Z(e,et(t)),offset:Z(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,i=null,n=et(e);for(;r>=0&&(i=e[r],!(n.timestamp-i.timestamp>(0,K.f)(.1)));)r--;if(!i)return{x:0,y:0};let s=(0,K.X)(n.timestamp-i.timestamp);if(0===s)return{x:0,y:0};let a={x:(n.x-i.x)/s,y:(n.y-i.y)/s};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function et(e){return e[e.length-1]}var er=r(4068),ei=r(7758);function en(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function es(e,t){let r=t.min-e.min,i=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,i]=[i,r]),{min:r,max:i}}function ea(e,t,r){return{min:eo(e,t),max:eo(e,r)}}function eo(e,t){return"number"==typeof e?e:e[t]||0}let el=new WeakMap;class eu{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=(0,B.ge)(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new Q(e,{onSessionStart:e=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(R(e).point)},onStart:(e,t)=>{let{drag:r,dragPropagation:i,onDragStart:n}=this.getProps();if(r&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(k[e])return null;else return k[e]=!0,()=>{k[e]=!1};return k.x||k.y?null:(k.x=k.y=!0,()=>{k.x=k.y=!1})}(r),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),U(e=>{let t=this.getAxisMotionValue(e).get()||0;if(A.KN.test(t)){let{projection:r}=this.visualElement;if(r&&r.layout){let i=r.layout.layoutBox[e];i&&(t=_(i)*(parseFloat(t)/100))}}this.originPoint[e]=t}),n&&T.Gt.postRender(()=>n(e,t)),(0,G.g)(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:r,dragDirectionLock:i,onDirectionLock:n,onDrag:s}=this.getProps();if(!r&&!this.openDragLock)return;let{offset:a}=t;if(i&&null===this.currentDirection){this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(a),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),s&&s(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>U(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:q(this.visualElement)})}stop(e,t){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:i}=t;this.startAnimation(i);let{onDragEnd:n}=this.getProps();n&&T.Gt.postRender(()=>n(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:i}=this.getProps();if(!r||!ec(e,i,this.currentDirection))return;let n=this.getAxisMotionValue(e),s=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(s=function(e,{min:t,max:r},i){return void 0!==t&&e<t?e=i?(0,E.k)(t,e,i.min):Math.max(e,t):void 0!==r&&e>r&&(e=i?(0,E.k)(r,e,i.max):Math.min(e,r)),e}(s,this.constraints[e],this.elastic[e])),n.set(s)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;e&&X(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&r?this.constraints=function(e,{top:t,left:r,bottom:i,right:n}){return{x:en(e.x,r,n),y:en(e.y,t,i)}}(r.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:ea(e,"left","right"),y:ea(e,"top","bottom")}}(t),i!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&U(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:r}=this.getProps();if(!t||!X(t))return!1;let i=t.current;(0,N.V)(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=(0,W.L)(i,n.root,this.visualElement.getTransformPagePoint()),a=(e=n.layout.layoutBox,{x:es(e.x,s.x),y:es(e.y,s.y)});if(r){let e=r((0,O.pA)(a));this.hasMutatedConstraints=!!e,e&&(a=(0,O.FY)(e))}return a}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:i,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(U(a=>{if(!ec(a,t,this.currentDirection))return;let l=o&&o[a]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:r?e[a]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return(0,G.g)(this.visualElement,e),r.start((0,S.f)(e,r,0,t,this.visualElement,!1))}stopAnimation(){U(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){U(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,r=this.visualElement.getProps();return r[t]||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){U(t=>{let{drag:r}=this.getProps();if(!ec(t,r,this.currentDirection))return;let{projection:i}=this.visualElement,n=this.getAxisMotionValue(t);if(i&&i.layout){let{min:r,max:s}=i.layout.layoutBox[t];n.set(e[t]-(0,E.k)(r,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!X(t)||!r||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};U(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let r=t.get();i[e]=function(e,t){let r=.5,i=_(e),n=_(t);return n>i?r=(0,er.q)(t.min,t.max-i,e.min):i>n&&(r=(0,er.q)(e.min,e.max-n,t.min)),(0,ei.q)(0,1,r)}({min:r,max:r},this.constraints[e])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),U(t=>{if(!ec(t,e,null))return;let r=this.getAxisMotionValue(t),{min:n,max:s}=this.constraints[t];r.set((0,E.k)(n,s,i[t]))})}addListeners(){if(!this.visualElement.current)return;el.set(this.visualElement,this);let e=V(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();X(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,i=r.addEventListener("measure",t);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),T.Gt.read(t);let n=C(window,"resize",()=>this.scalePositionWithinConstraints()),s=r.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(U(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{n(),e(),i(),s&&s()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:i,dragConstraints:n,dragElastic:s,dragMomentum:a}}}function ec(e,t,r){return(!0===t||t===e)&&(null===r||r===e)}class ed extends y{constructor(e){super(e),this.removeGroupControls=P.l,this.removeListeners=P.l,this.controls=new eu(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||P.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let eh=e=>(t,r)=>{e&&T.Gt.postRender(()=>e(t,r))};class ep extends y{constructor(){super(...arguments),this.removePointerDownListener=P.l}onPointerDown(e){this.session=new Q(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:q(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:eh(e),onStart:eh(t),onMove:r,onEnd:(e,t)=>{delete this.session,i&&T.Gt.postRender(()=>i(e,t))}}}mount(){this.removePointerDownListener=V(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var em=r(687);let{schedule:ef}=(0,r(9848).I)(queueMicrotask,!1);var eg=r(3210),ex=r(6044),ev=r(2157);let ey=(0,eg.createContext)({}),eb={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ew(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ej={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!A.px.test(e))return e;else e=parseFloat(e);let r=ew(e,t.target.x),i=ew(e,t.target.y);return`${r}% ${i}%`}};var eP=r(9664),ek=r(6633);class eA extends eg.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:i}=this.props,{projection:n}=e;(0,ek.$)(eE),n&&(t.group&&t.group.add(n),r&&r.register&&i&&r.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),eb.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:i,isPresent:n}=this.props,{projection:s}=r;return s&&(s.isPresent=n,i||e.layoutDependency!==t||void 0===t||e.isPresent!==n?s.willUpdate():this.safeToRemove(),e.isPresent!==n&&(n?s.promote():s.relegate()||T.Gt.postRender(()=>{let e=s.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),ef.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function eT(e){let[t,r]=(0,ex.xQ)(),i=(0,eg.useContext)(ev.L);return(0,em.jsx)(eA,{...e,layoutGroup:i,switchLayoutGroup:(0,eg.useContext)(ey),isPresent:t,safeToRemove:r})}let eE={borderRadius:{...ej,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ej,borderTopRightRadius:ej,borderBottomLeftRadius:ej,borderBottomRightRadius:ej,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let i=eP.f.parse(e);if(i.length>5)return e;let n=eP.f.createTransformer(e),s=+("number"!=typeof i[0]),a=r.x.scale*t.x,o=r.y.scale*t.y;i[0+s]/=a,i[1+s]/=o;let l=(0,E.k)(a,o,.5);return"number"==typeof i[2+s]&&(i[2+s]/=l),"number"==typeof i[3+s]&&(i[3+s]/=l),n(i)}}};var eN=r(2082),eS=r(4156),eC=r(3905),eM=r(2923),eR=r(4325),eD=r(6184),eV=r(4342),eO=r(4296),e_=r(5944),eF=r(722),eL=r(7556);let eI=(e,t)=>e.depth-t.depth;class ez{constructor(){this.children=[],this.isDirty=!1}add(e){(0,eL.Kq)(this.children,e),this.isDirty=!0}remove(e){(0,eL.Ai)(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(eI),this.isDirty=!1,this.children.forEach(e)}}var e$=r(5927);function eB(e){return(0,e$.S)(e)?e.get():e}var eU=r(2716);let eW=["TopLeft","TopRight","BottomLeft","BottomRight"],eq=eW.length,eX=e=>"string"==typeof e?parseFloat(e):e,eG=e=>"number"==typeof e||A.px.test(e);function eH(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let eK=eQ(0,.5,eU.yT),eY=eQ(.5,.95,P.l);function eQ(e,t,r){return i=>i<e?0:i>t?1:r((0,er.q)(e,t,i))}function eJ(e,t){e.min=t.min,e.max=t.max}function eZ(e,t){eJ(e.x,t.x),eJ(e.y,t.y)}function e0(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}var e1=r(2485);function e2(e,t,r,i,n){return e-=t,e=(0,e1.hq)(e,1/r,i),void 0!==n&&(e=(0,e1.hq)(e,1/n,i)),e}function e5(e,t,[r,i,n],s,a){!function(e,t=0,r=1,i=.5,n,s=e,a=e){if(A.KN.test(t)&&(t=parseFloat(t),t=(0,E.k)(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let o=(0,E.k)(s.min,s.max,i);e===s&&(o-=t),e.min=e2(e.min,t,r,o,n),e.max=e2(e.max,t,r,o,n)}(e,t[r],t[i],t[n],t.scale,s,a)}let e4=["x","scaleX","originX"],e3=["y","scaleY","originY"];function e6(e,t,r,i){e5(e.x,t,e4,r?r.x:void 0,i?i.x:void 0),e5(e.y,t,e3,r?r.y:void 0,i?i.y:void 0)}function e8(e){return 0===e.translate&&1===e.scale}function e7(e){return e8(e.x)&&e8(e.y)}function e9(e,t){return e.min===t.min&&e.max===t.max}function te(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function tt(e,t){return te(e.x,t.x)&&te(e.y,t.y)}function tr(e){return _(e.x)/_(e.y)}function ti(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class tn{constructor(){this.members=[]}add(e){(0,eL.Kq)(this.members,e),e.scheduleRender()}remove(e){if((0,eL.Ai)(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,r=this.members.findIndex(t=>e===t);if(0===r)return!1;for(let e=r;e>=0;e--){let r=this.members[e];if(!1!==r.isPresent){t=r;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:i}=e.options;!1===i&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}var ts=r(7606);let ta={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},to=["","X","Y","Z"],tl={visibility:"hidden"},tu=0;function tc(e,t,r,i){let{latestValues:n}=t;n[e]&&(r[e]=n[e],t.setStaticValue(e,0),i&&(i[e]=0))}function td({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:i,resetTransform:n}){return class{constructor(e={},r=t?.()){this.id=tu++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,eN.Q.value&&(ta.nodes=ta.calculatedTargetDeltas=ta.calculatedProjections=0),this.nodes.forEach(tm),this.nodes.forEach(tw),this.nodes.forEach(tj),this.nodes.forEach(tf),eN.Q.addProjectionMetrics&&eN.Q.addProjectionMetrics(ta)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new ez)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new eO.v),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=(0,eS.x)(t)&&!(0,eC.h)(t),this.instance=t;let{layoutId:r,layout:i,visualElement:n}=this.options;if(n&&!n.current&&n.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(i||r)&&(this.isLayoutDirty=!0),e){let r,i=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){let r=eR.k.now(),i=({timestamp:n})=>{let s=n-r;s>=250&&((0,T.WG)(i),e(s-t))};return T.Gt.setup(i,!0),()=>(0,T.WG)(i)}(i,250),eb.hasAnimatedSinceResize&&(eb.hasAnimatedSinceResize=!1,this.nodes.forEach(tb))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&n&&(r||i)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:r,layout:i})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||tN,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=n.getProps(),l=!this.targetLayout||!tt(this.targetLayout,i),u=!t&&r;if(this.options.layoutRoot||this.resumeFrom||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...(0,eM.r)(s,"layout"),onPlay:a,onComplete:o};(n.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,u)}else t||tb(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,T.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(tP),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:r}=t.options;if(!r)return;let i=(0,eF.P)(r);if(window.MotionHasOptimisedAnimation(i,"transform")){let{layout:e,layoutId:r}=t.options;window.MotionCancelOptimisedAnimation(i,"transform",T.Gt,!(e||r))}let{parent:n}=t;n&&!n.hasCheckedOptimisedAppear&&e(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;let i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(tx);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(tv);this.isUpdating||this.nodes.forEach(tv),this.animationCommitId=this.animationId,this.isUpdating=!1,this.nodes.forEach(ty),this.nodes.forEach(th),this.nodes.forEach(tp),this.clearAllSnapshots();let e=eR.k.now();T.uv.delta=(0,ei.q)(0,1e3/60,e-T.uv.timestamp),T.uv.timestamp=e,T.uv.isProcessing=!0,T.PP.update.process(T.uv),T.PP.preRender.process(T.uv),T.PP.render.process(T.uv),T.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ef.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(tg),this.sharedNodes.forEach(tk)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,T.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){T.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||_(this.snapshot.measuredBox.x)||_(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=(0,B.ge)(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=i(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!n)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!e7(this.projectionDelta),r=this.getTransformTemplate(),i=r?r(this.latestValues,""):void 0,s=i!==this.prevTransformTemplateValue;e&&this.instance&&(t||(0,ts.HD)(this.latestValues)||s)&&(n(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let r=this.measurePageBox(),i=this.removeElementScroll(r);return e&&(i=this.removeTransform(i)),tM((t=i).x),tM(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:i,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return(0,B.ge)();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(tD))){let{scroll:e}=this.root;e&&((0,e1.Ql)(t.x,e.offset.x),(0,e1.Ql)(t.y,e.offset.y))}return t}removeElementScroll(e){let t=(0,B.ge)();if(eZ(t,e),this.scroll?.wasRoot)return t;for(let r=0;r<this.path.length;r++){let i=this.path[r],{scroll:n,options:s}=i;i!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&eZ(t,e),(0,e1.Ql)(t.x,n.offset.x),(0,e1.Ql)(t.y,n.offset.y))}return t}applyTransform(e,t=!1){let r=(0,B.ge)();eZ(r,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];!t&&i.options.layoutScroll&&i.scroll&&i!==i.root&&(0,e1.Ww)(r,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),(0,ts.HD)(i.latestValues)&&(0,e1.Ww)(r,i.latestValues)}return(0,ts.HD)(this.latestValues)&&(0,e1.Ww)(r,this.latestValues),r}removeTransform(e){let t=(0,B.ge)();eZ(t,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];if(!r.instance||!(0,ts.HD)(r.latestValues))continue;(0,ts.vk)(r.latestValues)&&r.updateSnapshot();let i=(0,B.ge)();eZ(i,r.measurePageBox()),e6(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,i)}return(0,ts.HD)(this.latestValues)&&e6(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==T.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let r=!!this.resumingFrom||this!==t;if(!(e||r&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:i,layoutId:n}=this.options;if(this.layout&&(i||n)){if(this.resolvedRelativeTargetAt=T.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,B.ge)(),this.relativeTargetOrigin=(0,B.ge)(),$(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),eZ(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=(0,B.ge)(),this.targetWithTransforms=(0,B.ge)()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,a,o;this.forceRelativeParentToResolveTarget(),s=this.target,a=this.relativeTarget,o=this.relativeParent.target,I(s.x,a.x,o.x),I(s.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):eZ(this.target,this.layout.layoutBox),(0,e1.o4)(this.target,this.targetDelta)):eZ(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,B.ge)(),this.relativeTargetOrigin=(0,B.ge)(),$(this.relativeTargetOrigin,this.target,e.target),eZ(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}eN.Q.value&&ta.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||(0,ts.vk)(this.parent.latestValues)||(0,ts.vF)(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,r=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(r=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===T.uv.timestamp&&(r=!1),r)return;let{layout:i,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||n))return;eZ(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;(0,e1.OU)(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=(0,B.ge)());let{target:o}=e;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(e0(this.prevProjectionDelta.x,this.projectionDelta.x),e0(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),L(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&ti(this.projectionDelta.x,this.prevProjectionDelta.x)&&ti(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),eN.Q.value&&ta.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=(0,B.xU)(),this.projectionDelta=(0,B.xU)(),this.projectionDeltaWithTransform=(0,B.xU)()}setAnimationOrigin(e,t=!1){let r,i=this.snapshot,n=i?i.latestValues:{},s={...this.latestValues},a=(0,B.xU)();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=(0,B.ge)(),l=(i?i.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(tE));this.animationProgress=0,this.mixTargetDelta=t=>{let i=t/1e3;if(tA(a.x,e.x,i),tA(a.y,e.y,i),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,p,m,f,g;$(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,g=i,tT(p.x,m.x,f.x,g),tT(p.y,m.y,f.y,g),r&&(u=this.relativeTarget,h=r,e9(u.x,h.x)&&e9(u.y,h.y))&&(this.isProjectionDirty=!1),r||(r=(0,B.ge)()),eZ(r,this.relativeTarget)}l&&(this.animationValues=s,function(e,t,r,i,n,s){n?(e.opacity=(0,E.k)(0,r.opacity??1,eK(i)),e.opacityExit=(0,E.k)(t.opacity??1,0,eY(i))):s&&(e.opacity=(0,E.k)(t.opacity??1,r.opacity??1,i));for(let n=0;n<eq;n++){let s=`border${eW[n]}Radius`,a=eH(t,s),o=eH(r,s);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||eG(a)===eG(o)?(e[s]=Math.max((0,E.k)(eX(a),eX(o),i),0),(A.KN.test(o)||A.KN.test(a))&&(e[s]+="%")):e[s]=o)}(t.rotate||r.rotate)&&(e.rotate=(0,E.k)(t.rotate||0,r.rotate||0,i))}(s,n,this.latestValues,i,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=i},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,T.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=T.Gt.update(()=>{eb.hasAnimatedSinceResize=!0,eD.q.layout++,this.motionValue||(this.motionValue=(0,eV.OQ)(0)),this.currentAnimation=(0,e_.z)(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{eD.q.layout--},onComplete:()=>{eD.q.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:r,layout:i,latestValues:n}=e;if(t&&r&&i){if(this!==e&&this.layout&&i&&tR(this.options.animationType,this.layout.layoutBox,i.layoutBox)){r=this.target||(0,B.ge)();let t=_(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;let i=_(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+i}eZ(t,r),(0,e1.Ww)(t,n),L(this.projectionDeltaWithTransform,this.layoutCorrected,t,n)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new tn),this.sharedNodes.get(e).add(t);let r=t.options.initialPromotionConfig;t.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){let i=this.getStack();i&&i.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:r}=e;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(t=!0),!t)return;let i={};r.z&&tc("z",e,i,this.animationValues);for(let t=0;t<to.length;t++)tc(`rotate${to[t]}`,e,i,this.animationValues),tc(`skew${to[t]}`,e,i,this.animationValues);for(let t in e.render(),i)e.setStaticValue(t,i[t]),this.animationValues&&(this.animationValues[t]=i[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return tl;let t={visibility:""},r=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=eB(e?.pointerEvents)||"",t.transform=r?r(this.latestValues,""):"none",t;let i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=eB(e?.pointerEvents)||""),this.hasProjected&&!(0,ts.HD)(this.latestValues)&&(t.transform=r?r({},""):"none",this.hasProjected=!1),t}let n=i.animationValues||i.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,r){let i="",n=e.x.translate/t.x,s=e.y.translate/t.y,a=r?.z||0;if((n||s||a)&&(i=`translate3d(${n}px, ${s}px, ${a}px) `),(1!==t.x||1!==t.y)&&(i+=`scale(${1/t.x}, ${1/t.y}) `),r){let{transformPerspective:e,rotate:t,rotateX:n,rotateY:s,skewX:a,skewY:o}=r;e&&(i=`perspective(${e}px) ${i}`),t&&(i+=`rotate(${t}deg) `),n&&(i+=`rotateX(${n}deg) `),s&&(i+=`rotateY(${s}deg) `),a&&(i+=`skewX(${a}deg) `),o&&(i+=`skewY(${o}deg) `)}let o=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==o||1!==l)&&(i+=`scale(${o}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),r&&(t.transform=r(n,t.transform));let{x:s,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*s.origin}% ${100*a.origin}% 0`,i.animationValues?t.opacity=i===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:t.opacity=i===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,ek.H){if(void 0===n[e])continue;let{correct:r,applyTo:s,isCSSVariable:a}=ek.H[e],o="none"===t.transform?n[e]:r(n[e],i);if(s){let e=s.length;for(let r=0;r<e;r++)t[s[r]]=o}else a?this.options.visualElement.renderState.vars[e]=o:t[e]=o}return this.options.layoutId&&(t.pointerEvents=i===this?eB(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(tx),this.root.sharedNodes.clear()}}}function th(e){e.updateLayout()}function tp(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:r,measuredBox:i}=e.layout,{animationType:n}=e.options,s=t.source!==e.layout.source;"size"===n?U(e=>{let i=s?t.measuredBox[e]:t.layoutBox[e],n=_(i);i.min=r[e].min,i.max=i.min+n}):tR(n,t.layoutBox,r)&&U(i=>{let n=s?t.measuredBox[i]:t.layoutBox[i],a=_(r[i]);n.max=n.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[i].max=e.relativeTarget[i].min+a)});let a=(0,B.xU)();L(a,r,t.layoutBox);let o=(0,B.xU)();s?L(o,e.applyTransform(i,!0),t.measuredBox):L(o,r,t.layoutBox);let l=!e7(a),u=!1;if(!e.resumeFrom){let i=e.getClosestProjectingParent();if(i&&!i.resumeFrom){let{snapshot:n,layout:s}=i;if(n&&s){let a=(0,B.ge)();$(a,t.layoutBox,n.layoutBox);let o=(0,B.ge)();$(o,r,s.layoutBox),tt(a,o)||(u=!0),i.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=i)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:t,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function tm(e){eN.Q.value&&ta.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function tf(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function tg(e){e.clearSnapshot()}function tx(e){e.clearMeasurements()}function tv(e){e.isLayoutDirty=!1}function ty(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function tb(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function tw(e){e.resolveTargetDelta()}function tj(e){e.calcProjection()}function tP(e){e.resetSkewAndRotation()}function tk(e){e.removeLeadSnapshot()}function tA(e,t,r){e.translate=(0,E.k)(t.translate,0,r),e.scale=(0,E.k)(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function tT(e,t,r,i){e.min=(0,E.k)(t.min,r.min,i),e.max=(0,E.k)(t.max,r.max,i)}function tE(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let tN={duration:.45,ease:[.4,0,.1,1]},tS=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),tC=tS("applewebkit/")&&!tS("chrome/")?Math.round:P.l;function tM(e){e.min=tC(e.min),e.max=tC(e.max)}function tR(e,t,r){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(tr(t)-tr(r)))}function tD(e){return e!==e.root&&e.scroll?.wasRoot}let tV=td({attachResizeListener:(e,t)=>C(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),tO={current:void 0},t_=td({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!tO.current){let e=new tV({});e.mount(window),e.setOptions({layoutScroll:!0}),tO.current=e}return tO.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});var tF=r(9292);function tL(e,t){let r=(0,tF.K)(e),i=new AbortController;return[r,{passive:!0,...t,signal:i.signal},()=>i.abort()]}function tI(e){return!("touch"===e.pointerType||k.x||k.y)}function tz(e,t,r){let{props:i}=e;e.animationState&&i.whileHover&&e.animationState.setActive("whileHover","Start"===r);let n=i["onHover"+r];n&&T.Gt.postRender(()=>n(t,R(t)))}class t$ extends y{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[i,n,s]=tL(e,r),a=e=>{if(!tI(e))return;let{target:r}=e,i=t(r,e);if("function"!=typeof i||!r)return;let s=e=>{tI(e)&&(i(e),r.removeEventListener("pointerleave",s))};r.addEventListener("pointerleave",s,n)};return i.forEach(e=>{e.addEventListener("pointerenter",a,n)}),s}(e,(e,t)=>(tz(this.node,t,"Start"),e=>tz(this.node,e,"End"))))}unmount(){}}class tB extends y{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,H.F)(C(this.node.current,"focus",()=>this.onFocus()),C(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var tU=r(8171);let tW=(e,t)=>!!t&&(e===t||tW(e,t.parentElement)),tq=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),tX=new WeakSet;function tG(e){return t=>{"Enter"===t.key&&e(t)}}function tH(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let tK=(e,t)=>{let r=e.currentTarget;if(!r)return;let i=tG(()=>{if(tX.has(r))return;tH(r,"down");let e=tG(()=>{tH(r,"up")});r.addEventListener("keyup",e,t),r.addEventListener("blur",()=>tH(r,"cancel"),t)});r.addEventListener("keydown",i,t),r.addEventListener("blur",()=>r.removeEventListener("keydown",i),t)};function tY(e){return M(e)&&!(k.x||k.y)}function tQ(e,t,r){let{props:i}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&i.whileTap&&e.animationState.setActive("whileTap","Start"===r);let n=i["onTap"+("End"===r?"":r)];n&&T.Gt.postRender(()=>n(t,R(t)))}class tJ extends y{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[i,n,s]=tL(e,r),a=e=>{let i=e.currentTarget;if(!tY(e))return;tX.add(i);let s=t(i,e),a=(e,t)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),tX.has(i)&&tX.delete(i),tY(e)&&"function"==typeof s&&s(e,{success:t})},o=e=>{a(e,i===window||i===document||r.useGlobalTarget||tW(i,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",o,n),window.addEventListener("pointercancel",l,n)};return i.forEach(e=>{((r.useGlobalTarget?window:e).addEventListener("pointerdown",a,n),(0,tU.s)(e))&&(e.addEventListener("focus",e=>tK(e,n)),tq.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),s}(e,(e,t)=>(tQ(this.node,t,"Start"),(e,{success:t})=>tQ(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let tZ=new WeakMap,t0=new WeakMap,t1=e=>{let t=tZ.get(e.target);t&&t(e)},t2=e=>{e.forEach(t1)},t5={some:0,all:1};class t4 extends y{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:i="some",once:n}=e,s={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof i?i:t5[i]};return function(e,t,r){let i=function({root:e,...t}){let r=e||document;t0.has(r)||t0.set(r,{});let i=t0.get(r),n=JSON.stringify(t);return i[n]||(i[n]=new IntersectionObserver(t2,{root:e,...t})),i[n]}(t);return tZ.set(e,r),i.observe(e),()=>{tZ.delete(e),i.unobserve(e)}}(this.node.current,s,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,n&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:r,onViewportLeave:i}=this.node.getProps(),s=t?r:i;s&&s(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t))&&this.startObserver()}unmount(){}}let t3=(0,eg.createContext)({strict:!1});var t6=r(2582);let t8=(0,eg.createContext)({});var t7=r(7529);function t9(e){return Array.isArray(e)?e.join(" "):e}var re=r(7044),rt=r(9240);let rr=Symbol.for("motionComponentSymbol");var ri=r(1756),rn=r(1279),rs=r(2743),ra=r(7609),ro=r(8744);let rl=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ru(e,t,r){for(let i in t)(0,e$.S)(t[i])||(0,ra.z)(i,r)||(e[i]=t[i])}var rc=r(2702);let rd=()=>({...rl(),attrs:{}});var rh=r(9197);let rp=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function rm(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||rp.has(e)}let rf=e=>!rm(e);try{!function(e){"function"==typeof e&&(rf=t=>t.startsWith("on")?!rm(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let rg=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function rx(e){if("string"!=typeof e||e.includes("-"));else if(rg.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var rv=r(8337),ry=r(2789);let rb=e=>(t,r)=>{let i=(0,eg.useContext)(t8),n=(0,eg.useContext)(rn.t),a=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},r,i,n){return{latestValues:function(e,t,r,i){let n={},a=i(e,{});for(let e in a)n[e]=eB(a[e]);let{initial:o,animate:l}=e,u=(0,t7.e)(e),c=(0,t7.O)(e);t&&c&&!u&&!1!==e.inherit&&(void 0===o&&(o=t.initial),void 0===l&&(l=t.animate));let d=!!r&&!1===r.initial,h=(d=d||!1===o)?l:o;if(h&&"boolean"!=typeof h&&!(0,s.N)(h)){let t=Array.isArray(h)?h:[h];for(let r=0;r<t.length;r++){let i=(0,rv.a)(e,t[r]);if(i){let{transitionEnd:e,transition:t,...r}=i;for(let e in r){let t=r[e];if(Array.isArray(t)){let e=d?t.length-1:0;t=t[e]}null!==t&&(n[e]=t)}for(let t in e)n[t]=e[t]}}}return n}(r,i,n,e),renderState:t()}})(e,t,i,n);return r?a():(0,ry.M)(a)},rw={useVisualState:rb({scrapeMotionValuesFromProps:r(5934).x,createRenderState:rl})},rj={useVisualState:rb({scrapeMotionValuesFromProps:r(8605).x,createRenderState:rd})};var rP=r(515),rk=r(8778);let rA=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(r,i)=>"create"===i?e:(t.has(i)||t.set(i,e(i)),t.get(i))})}((i={animation:{Feature:b},exit:{Feature:j},inView:{Feature:t4},tap:{Feature:tJ},focus:{Feature:tB},hover:{Feature:t$},pan:{Feature:ep},drag:{Feature:ed,ProjectionNode:t_,MeasureLayout:eT},layout:{ProjectionNode:t_,MeasureLayout:eT}},n=(e,t)=>rx(e)?new rk.l(t):new rP.M(t,{allowProjection:e!==eg.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:i,Component:n}){function s(e,s){var a,o,l;let u,c={...(0,eg.useContext)(t6.Q),...e,layoutId:function({layoutId:e}){let t=(0,eg.useContext)(ev.L).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:d}=c,p=function(e){let{initial:t,animate:r}=function(e,t){if((0,t7.e)(e)){let{initial:t,animate:r}=e;return{initial:!1===t||(0,h.w)(t)?t:void 0,animate:(0,h.w)(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,eg.useContext)(t8));return(0,eg.useMemo)(()=>({initial:t,animate:r}),[t9(t),t9(r)])}(e),m=i(e,d);if(!d&&re.B){o=0,l=0,(0,eg.useContext)(t3).strict;let e=function(e){let{drag:t,layout:r}=rt.B;if(!t&&!r)return{};let i={...t,...r};return{MeasureLayout:t?.isEnabled(e)||r?.isEnabled(e)?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(c);u=e.MeasureLayout,p.visualElement=function(e,t,r,i,n){let{visualElement:s}=(0,eg.useContext)(t8),a=(0,eg.useContext)(t3),o=(0,eg.useContext)(rn.t),l=(0,eg.useContext)(t6.Q).reducedMotion,u=(0,eg.useRef)(null);i=i||a.renderer,!u.current&&i&&(u.current=i(e,{visualState:t,parent:s,props:r,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let c=u.current,d=(0,eg.useContext)(ey);c&&!c.projection&&n&&("html"===c.type||"svg"===c.type)&&function(e,t,r,i){let{layoutId:n,layout:s,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new r(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!a||o&&X(o),visualElement:e,animationType:"string"==typeof s?s:"both",initialPromotionConfig:i,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,r,n,d);let h=(0,eg.useRef)(!1);(0,eg.useInsertionEffect)(()=>{c&&h.current&&c.update(r,o)});let p=r[ri.n],m=(0,eg.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,rs.E)(()=>{c&&(h.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),ef.render(c.render),m.current&&c.animationState&&c.animationState.animateChanges())}),(0,eg.useEffect)(()=>{c&&(!m.current&&c.animationState&&c.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),c}(n,m,c,t,e.ProjectionNode)}return(0,em.jsxs)(t8.Provider,{value:p,children:[u&&p.visualElement?(0,em.jsx)(u,{visualElement:p.visualElement,...c}):null,r(n,e,(a=p.visualElement,(0,eg.useCallback)(e=>{e&&m.onMount&&m.onMount(e),a&&(e?a.mount(e):a.unmount()),s&&("function"==typeof s?s(e):X(s)&&(s.current=e))},[a])),m,d,p.visualElement)]})}e&&function(e){for(let t in e)rt.B[t]={...rt.B[t],...e[t]}}(e),s.displayName=`motion.${"string"==typeof n?n:`create(${n.displayName??n.name??""})`}`;let a=(0,eg.forwardRef)(s);return a[rr]=n,a}({...rx(e)?rj:rw,preloadedFeatures:i,useRender:function(e=!1){return(t,r,i,{latestValues:n},s)=>{let a=(rx(t)?function(e,t,r,i){let n=(0,eg.useMemo)(()=>{let r=rd();return(0,rc.B)(r,t,(0,rh.n)(i),e.transformTemplate,e.style),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};ru(t,e.style,e),n.style={...t,...n.style}}return n}:function(e,t){let r={},i=function(e,t){let r=e.style||{},i={};return ru(i,r,e),Object.assign(i,function({transformTemplate:e},t){return(0,eg.useMemo)(()=>{let r=rl();return(0,ro.O)(r,t,e),Object.assign({},r.vars,r.style)},[t])}(e,t)),i}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r})(r,n,s,t),o=function(e,t,r){let i={};for(let n in e)("values"!==n||"object"!=typeof e.values)&&(rf(n)||!0===r&&rm(n)||!t&&!rm(n)||e.draggable&&n.startsWith("onDrag"))&&(i[n]=e[n]);return i}(r,"string"==typeof t,e),l=t!==eg.Fragment?{...o,...a,ref:i}:{},{children:u}=r,c=(0,eg.useMemo)(()=>(0,e$.S)(u)?u.get():u,[u]);return(0,eg.createElement)(t,{...l,children:c})}}(t),createVisualElement:n,Component:e})}))},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var i=r(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},515:(e,t,r)=>{"use strict";r.d(t,{M:()=>d});var i=r(5726),n=r(9602),s=r(2238),a=r(2953),o=r(1048),l=r(8744),u=r(3088),c=r(5934);class d extends o.b{constructor(){super(...arguments),this.type="html",this.renderInstance=u.e}readValueFromInstance(e,t){if(i.f.has(t))return this.projection?.isProjecting?(0,n.zs)(t):(0,n.Ib)(e,t);{let r=window.getComputedStyle(e),i=((0,s.j)(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof i?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:t}){return(0,a.m)(e,t)}build(e,t,r){(0,l.O)(e,t,r.transformTemplate)}scrapeMotionValuesFromProps(e,t,r){return(0,c.x)(e,t,r)}}},554:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},567:(e,t,r)=>{"use strict";function i(e){return"string"==typeof e||Array.isArray(e)}r.d(t,{w:()=>i})},660:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function i(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return i}})},722:(e,t,r)=>{"use strict";r.d(t,{P:()=>n});var i=r(1756);function n(e){return e.props[i.n]}},736:(e,t,r)=>{"use strict";r.d(t,{h:()=>h,q:()=>d});var i=r(9076),n=r(3671);let s=new Set,a=!1,o=!1,l=!1;function u(){if(o){let e=Array.from(s).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),r=new Map;t.forEach(e=>{let t=(0,i.W9)(e);t.length&&(r.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=r.get(e);t&&t.forEach(([t,r])=>{e.getValue(t)?.set(r)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}o=!1,a=!1,s.forEach(e=>e.complete(l)),s.clear()}function c(){s.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(o=!0)})}function d(){l=!0,c(),u(),l=!1}class h{constructor(e,t,r,i,n,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=r,this.motionValue=i,this.element=n,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(s.add(this),a||(a=!0,n.Gt.read(c),n.Gt.resolveKeyframes(u))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:r,motionValue:i}=this;if(null===e[0]){let n=i?.get(),s=e[e.length-1];if(void 0!==n)e[0]=n;else if(r&&t){let i=r.readValue(t,s);null!=i&&(e[0]=i)}void 0===e[0]&&(e[0]=s),i&&void 0===n&&i.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),s.delete(this)}cancel(){"scheduled"===this.state&&(s.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}},748:(e,t,r)=>{"use strict";r.d(t,{W:()=>o});var i=r(5444);let n={...i.ai,transform:Math.round};var s=r(2874);let a={rotate:s.uj,rotateX:s.uj,rotateY:s.uj,rotateZ:s.uj,scale:i.hs,scaleX:i.hs,scaleY:i.hs,scaleZ:i.hs,skew:s.uj,skewX:s.uj,skewY:s.uj,distance:s.px,translateX:s.px,translateY:s.px,translateZ:s.px,x:s.px,y:s.px,z:s.px,perspective:s.px,transformPerspective:s.px,opacity:i.X4,originX:s.gQ,originY:s.gQ,originZ:s.px},o={borderWidth:s.px,borderTopWidth:s.px,borderRightWidth:s.px,borderBottomWidth:s.px,borderLeftWidth:s.px,borderRadius:s.px,radius:s.px,borderTopLeftRadius:s.px,borderTopRightRadius:s.px,borderBottomRightRadius:s.px,borderBottomLeftRadius:s.px,width:s.px,maxWidth:s.px,height:s.px,maxHeight:s.px,top:s.px,right:s.px,bottom:s.px,left:s.px,padding:s.px,paddingTop:s.px,paddingRight:s.px,paddingBottom:s.px,paddingLeft:s.px,margin:s.px,marginTop:s.px,marginRight:s.px,marginBottom:s.px,marginLeft:s.px,backgroundPositionX:s.px,backgroundPositionY:s.px,...a,zIndex:n,fillOpacity:i.X4,strokeOpacity:i.X4,numOctaves:n}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},912:(e,t,r)=>{"use strict";r.d(t,{Sz:()=>a,ZZ:()=>l,dg:()=>o});var i=r(1455),n=r(2441),s=r(8830);let a=(0,i.A)(.33,1.53,.69,.99),o=(0,s.G)(a),l=(0,n.V)(o)},1008:(e,t,r)=>{"use strict";function i(e){return"function"==typeof e&&"applyToOptions"in e}r.d(t,{W:()=>i})},1043:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});let i=new Set(["width","height","top","left","right","bottom",...r(5726).U])},1048:(e,t,r)=>{"use strict";r.d(t,{b:()=>v});var i=r(1043),n=r(5472),s=r(6244),a=r(4278),o=r(2238);let l=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;var u=r(736),c=r(6954),d=r(9664),h=r(9837);let p=new Set(["auto","none","0"]);var m=r(9076);class f extends u.h{constructor(e,t,r,i,n){super(e,t,r,i,n,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:r}=this;if(!t||!t.current)return;super.readKeyframes();for(let r=0;r<e.length;r++){let i=e[r];if("string"==typeof i&&(i=i.trim(),(0,o.p)(i))){let n=function e(t,r,i=1){(0,s.V)(i<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[n,u]=function(e){let t=l.exec(e);if(!t)return[,];let[,r,i,n]=t;return[`--${r??i}`,n]}(t);if(!n)return;let c=window.getComputedStyle(r).getPropertyValue(n);if(c){let e=c.trim();return(0,a.i)(e)?parseFloat(e):e}return(0,o.p)(u)?e(u,r,i+1):u}(i,t.current);void 0!==n&&(e[r]=n),r===e.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!i.$.has(r)||2!==e.length)return;let[u,c]=e,d=(0,n.n)(u),h=(0,n.n)(c);if(d!==h)if((0,m.E4)(d)&&(0,m.E4)(h))for(let t=0;t<e.length;t++){let r=e[t];"string"==typeof r&&(e[t]=parseFloat(r))}else m.Hr[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,r=[];for(let t=0;t<e.length;t++){var i;(null===e[t]||("number"==typeof(i=e[t])?0===i:null===i||"none"===i||"0"===i||(0,c.$)(i)))&&r.push(t)}r.length&&function(e,t,r){let i,n=0;for(;n<e.length&&!i;){let t=e[n];"string"==typeof t&&!p.has(t)&&(0,d.V)(t).values.length&&(i=e[n]),n++}if(i&&r)for(let n of t)e[n]=(0,h.J)(r,i)}(e,r,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:r}=this;if(!e||!e.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=m.Hr[r](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let i=t[t.length-1];void 0!==i&&e.getValue(r,i).jump(i,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:r}=this;if(!e||!e.current)return;let i=e.getValue(t);i&&i.jump(this.measuredOrigin,!1);let n=r.length-1,s=r[n];r[n]=m.Hr[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,r])=>{e.getValue(t).set(r)}),this.resolveNoneKeyframes()}}var g=r(5927),x=r(9542);class v extends x.B{constructor(){super(...arguments),this.KeyframeResolver=f}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;(0,g.S)(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}},1062:(e,t,r)=>{"use strict";r.d(t,{Y:()=>n});var i=r(5547);function n(e,t,r){let n=Math.max(t-5,0);return(0,i.f)(r-e(n),t-n)}},1135:()=>{},1204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var i=r(7413),n=r(3587),s=r(9052),a=r(3427),o=r(9579),l=r(4647),u=r(6017),c=r(4295);function d(){return(0,i.jsxs)("main",{className:"min-h-screen relative z-10 bg-gradient-to-br from-neutral-50 via-white to-neutral-100",children:[(0,i.jsx)(n.Navbar,{}),(0,i.jsx)(a.Hero,{}),(0,i.jsx)(o.Services,{}),(0,i.jsx)(u.Gallery,{}),(0,i.jsx)(l.Testimonials,{}),(0,i.jsx)(c.Contact,{}),(0,i.jsx)(s.Footer,{})]})}},1279:(e,t,r)=>{"use strict";r.d(t,{t:()=>i});let i=(0,r(3210).createContext)(null)},1328:(e,t,r)=>{"use strict";r.d(t,{U:()=>i,_:()=>n});let i=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],n=["initial",...i]},1350:(e,t,r)=>{"use strict";r.d(t,{Footer:()=>f});var i=r(687),n=r(331),s=r(2688);let a=(0,s.A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),o=(0,s.A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),l=(0,s.A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);var u=r(4398),c=r(9891),d=r(6561),h=r(8340),p=r(1550),m=r(7992);function f(){let e=new Date().getFullYear(),t=[{icon:(0,i.jsx)(a,{className:"w-5 h-5"}),href:"#",name:"Facebook"},{icon:(0,i.jsx)(o,{className:"w-5 h-5"}),href:"#",name:"Instagram"},{icon:(0,i.jsx)(l,{className:"w-5 h-5"}),href:"#",name:"Twitter"}];return(0,i.jsxs)("footer",{className:"bg-gradient-to-b from-neutral-900 to-black text-white relative overflow-hidden",children:[(0,i.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-yellow-500 to-blue-500"}),(0,i.jsx)("div",{className:"absolute top-10 right-10 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl"}),(0,i.jsx)("div",{className:"absolute bottom-10 left-10 w-64 h-64 bg-yellow-500/10 rounded-full blur-3xl"}),(0,i.jsxs)("div",{className:"container mx-auto px-4 pt-16 pb-8 relative z-10",children:[(0,i.jsxs)("div",{className:"grid lg:grid-cols-4 md:grid-cols-2 gap-8 mb-12",children:[(0,i.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"lg:col-span-1",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-yellow-600 rounded-lg flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-white font-bold text-xl",children:"J"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-xl font-bold",children:"Jon Four Corners"}),(0,i.jsx)("p",{className:"text-sm text-neutral-400",children:"Detailing"})]})]}),(0,i.jsx)("p",{className:"text-neutral-300 leading-relaxed mb-6",children:"Premium automotive detailing services with artisan-level craftsmanship. Protecting your investment with meticulous attention to every corner."}),(0,i.jsxs)("div",{className:"flex items-center gap-4 text-sm text-neutral-400",children:[(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)(u.A,{className:"w-4 h-4 fill-yellow-400 text-yellow-400"}),(0,i.jsx)("span",{children:"5.0 Rating"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)(c.A,{className:"w-4 h-4 text-blue-400"}),(0,i.jsx)("span",{children:"Insured"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)(d.A,{className:"w-4 h-4 text-yellow-400"}),(0,i.jsx)("span",{children:"Certified"})]})]})]}),(0,i.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1},viewport:{once:!0},children:[(0,i.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Quick Links"}),(0,i.jsx)("ul",{className:"space-y-3",children:[{name:"Services",href:"#services"},{name:"Gallery",href:"#gallery"},{name:"About",href:"#about"},{name:"Contact",href:"#contact"},{name:"Reviews",href:"#testimonials"}].map(e=>(0,i.jsx)("li",{children:(0,i.jsx)(n.P.a,{href:e.href,whileHover:{x:5},className:"text-neutral-300 hover:text-white transition-colors duration-200",children:e.name})},e.name))})]}),(0,i.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},children:[(0,i.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Our Services"}),(0,i.jsx)("ul",{className:"space-y-3",children:["Ceramic Coating","Paint Correction","Interior Detailing","Mobile Service","Vehicle Restoration"].map(e=>(0,i.jsx)("li",{children:(0,i.jsx)("span",{className:"text-neutral-300",children:e})},e))})]}),(0,i.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},children:[(0,i.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Get In Touch"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)(h.A,{className:"w-5 h-5 text-blue-400"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-white font-medium",children:"(*************"}),(0,i.jsx)("p",{className:"text-sm text-neutral-400",children:"Call or text anytime"})]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)(p.A,{className:"w-5 h-5 text-blue-400"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-white font-medium",children:"<EMAIL>"}),(0,i.jsx)("p",{className:"text-sm text-neutral-400",children:"Quick response guaranteed"})]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)(m.A,{className:"w-5 h-5 text-blue-400"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-white font-medium",children:"Four Corners Region"}),(0,i.jsx)("p",{className:"text-sm text-neutral-400",children:"Mobile service available"})]})]})]}),(0,i.jsxs)("div",{className:"mt-6",children:[(0,i.jsx)("h5",{className:"text-sm font-semibold mb-3",children:"Follow Us"}),(0,i.jsx)("div",{className:"flex gap-3",children:t.map(e=>(0,i.jsx)(n.P.a,{href:e.href,whileHover:{scale:1.1,y:-2},whileTap:{scale:.95},className:"w-10 h-10 bg-neutral-800 rounded-lg flex items-center justify-center text-neutral-400 hover:text-white hover:bg-gradient-to-r hover:from-blue-600 hover:to-yellow-600 transition-all duration-300",children:e.icon},e.name))})]})]})]}),(0,i.jsx)(n.P.div,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"border-t border-neutral-800 pt-8",children:(0,i.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center gap-4",children:[(0,i.jsxs)("div",{className:"text-sm text-neutral-400",children:["\xa9 ",e," Jon Four Corners Detailing. All rights reserved."]}),(0,i.jsxs)("div",{className:"flex gap-6 text-sm text-neutral-400",children:[(0,i.jsx)(n.P.a,{href:"#",whileHover:{color:"#ffffff"},className:"hover:text-white transition-colors duration-200",children:"Privacy Policy"}),(0,i.jsx)(n.P.a,{href:"#",whileHover:{color:"#ffffff"},className:"hover:text-white transition-colors duration-200",children:"Terms of Service"}),(0,i.jsx)(n.P.a,{href:"#",whileHover:{color:"#ffffff"},className:"hover:text-white transition-colors duration-200",children:"Warranty"})]})]})}),(0,i.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.5},viewport:{once:!0},className:"text-center mt-8 pt-8 border-t border-neutral-800",children:[(0,i.jsx)("p",{className:"text-neutral-300 mb-4",children:"Ready to give your vehicle the premium care it deserves?"}),(0,i.jsx)(n.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-gradient-to-r from-blue-600 to-yellow-600 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300",children:"Get Your Free Quote Today"})]})]})]})}},1352:(e,t,r)=>{"use strict";r.d(t,{Navbar:()=>p});var i=r(687),n=r(3210),s=r(331),a=r(8920),o=r(8340),l=r(7992),u=r(2688);let c=(0,u.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),d=(0,u.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]);var h=r(2145);function p(){let[e,t]=(0,n.useState)(!1),[r,u]=(0,n.useState)(!1),p=[{name:"Services",href:"#services"},{name:"Process",href:"#process"},{name:"Gallery",href:"#gallery"},{name:"About",href:"#about"},{name:"Contact",href:"#contact"}];return(0,i.jsxs)(s.P.nav,{initial:{y:-100},animate:{y:0},transition:{duration:.6,ease:"easeOut"},className:`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${r?"backdrop-blur-luxury bg-white/80 luxury-shadow border-b border-white/30":"bg-transparent"}`,children:[(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between h-16 md:h-20",children:[(0,i.jsxs)(s.P.div,{whileHover:{scale:1.05},className:"flex items-center space-x-3 magnetic-hover",children:[(0,i.jsx)("div",{className:"w-12 h-12 luxury-gradient rounded-xl flex items-center justify-center luxury-shadow",children:(0,i.jsx)("span",{className:"text-white font-bold text-xl",children:"J"})}),(0,i.jsxs)("div",{className:"hidden sm:block",children:[(0,i.jsx)("h1",{className:"text-xl font-bold text-neutral-800",children:"Jon Four Corners"}),(0,i.jsx)("p",{className:"text-xs text-neutral-600 -mt-1 font-medium",children:"Detailing"})]})]}),(0,i.jsx)("div",{className:"hidden md:flex items-center space-x-8",children:p.map(e=>(0,i.jsx)(s.P.a,{href:e.href,whileHover:{y:-2},className:"text-neutral-700 hover:text-blue-600 transition-colors duration-200 font-medium",children:e.name},e.name))}),(0,i.jsxs)("div",{className:"hidden lg:flex items-center space-x-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-neutral-600",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,i.jsx)(o.A,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:"(*************"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,i.jsx)(l.A,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:"Four Corners Area"})]})]}),(0,i.jsx)(h.z,{containerClassName:"rounded-full",className:"bg-transparent text-neutral-800 px-6 py-2 text-sm font-semibold",children:"Get Quote"})]}),(0,i.jsx)(s.P.button,{whileTap:{scale:.95},onClick:()=>t(!e),className:"md:hidden p-2 rounded-lg bg-neutral-100 text-neutral-700",children:e?(0,i.jsx)(c,{className:"w-6 h-6"}):(0,i.jsx)(d,{className:"w-6 h-6"})})]})}),(0,i.jsx)(a.N,{children:e&&(0,i.jsx)(s.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:"md:hidden bg-white/95 backdrop-blur-md border-t border-neutral-200/50",children:(0,i.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,i.jsxs)("div",{className:"flex flex-col space-y-4",children:[p.map((e,r)=>(0,i.jsx)(s.P.a,{href:e.href,initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*r},onClick:()=>t(!1),className:"text-neutral-700 hover:text-blue-600 transition-colors duration-200 font-medium py-2",children:e.name},e.name)),(0,i.jsxs)("div",{className:"pt-4 border-t border-neutral-200",children:[(0,i.jsxs)("div",{className:"flex flex-col space-y-2 text-sm text-neutral-600 mb-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(o.A,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:"(*************"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(l.A,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:"Four Corners Area"})]})]}),(0,i.jsx)(h.z,{containerClassName:"rounded-full w-full",className:"bg-transparent text-neutral-800 px-6 py-3 text-sm font-semibold w-full text-center",children:"Get Free Quote"})]})]})})})})]})}},1437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return n},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return s}});let i=r(4722),n=["(..)(..)","(.)","(..)","(...)"];function s(e){return void 0!==e.split("/").find(e=>n.find(t=>e.startsWith(t)))}function a(e){let t,r,s;for(let i of e.split("/"))if(r=n.find(e=>i.startsWith(e))){[t,s]=e.split(r,2);break}if(!t||!r||!s)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,i.normalizeAppPath)(t),r){case"(.)":s="/"===t?"/"+s:t+"/"+s;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});s=t.split("/").slice(0,-1).concat(s).join("/");break;case"(...)":s="/"+s;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});s=a.slice(0,-2).concat(s).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:s}}},1455:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(3361);let n=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function s(e,t,r,s){if(e===t&&r===s)return i.l;let a=t=>(function(e,t,r,i,s){let a,o,l=0;do(a=n(o=t+(r-t)/2,i,s)-e)>0?r=o:t=o;while(Math.abs(a)>1e-7&&++l<12);return o})(t,0,1,e,r);return e=>0===e||1===e?e:n(a(e),t,s)}},1550:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(2688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},1565:(e,t,r)=>{"use strict";r.d(t,{$:()=>h});var i=r(2923),n=r(3671),s=r(1043),a=r(4342),o=r(7292),l=r(4693),u=r(7283),c=r(722),d=r(7238);function h(e,t,{delay:r=0,transitionOverride:p,type:m}={}){let{transition:f=e.getDefaultTransition(),transitionEnd:g,...x}=t;p&&(f=p);let v=[],y=m&&e.animationState&&e.animationState.getState()[m];for(let t in x){let a=e.getValue(t,e.latestValues[t]??null),o=x[t];if(void 0===o||y&&function({protectedKeys:e,needsAnimating:t},r){let i=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,i}(y,t))continue;let l={delay:r,...(0,i.r)(f||{},t)},h=a.get();if(void 0!==h&&!a.isAnimating&&!Array.isArray(o)&&o===h&&!l.velocity)continue;let p=!1;if(window.MotionHandoffAnimation){let r=(0,c.P)(e);if(r){let e=window.MotionHandoffAnimation(r,t,n.Gt);null!==e&&(l.startTime=e,p=!0)}}(0,u.g)(e,t),a.start((0,d.f)(t,a,o,e.shouldReduceMotion&&s.$.has(t)?{type:!1}:l,e,p));let m=a.animation;m&&v.push(m)}return g&&Promise.all(v).then(()=>{n.Gt.update(()=>{g&&function(e,t){let{transitionEnd:r={},transition:i={},...n}=(0,l.K)(e,t)||{};for(let t in n={...n,...r}){var s;let r=(s=n[t],(0,o.p)(s)?s[s.length-1]||0:s);e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,(0,a.OQ)(r))}}(e,g)})}),v}},1658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return h},normalizeMetadataPageToRoute:function(){return m},normalizeMetadataRoute:function(){return p}});let i=r(8304),n=function(e){return e&&e.__esModule?e:{default:e}}(r(8671)),s=r(6341),a=r(4396),o=r(660),l=r(4722),u=r(2958),c=r(5499);function d(e){let t=n.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,c.isGroupSegment)(e)||(0,c.isParallelRouteSegment)(e))&&(r=(0,o.djb2Hash)(t).toString(36).slice(0,6)),r}function h(e,t,r){let i=(0,l.normalizeAppPath)(e),o=(0,a.getNamedRouteRegex)(i,{prefixRouteKeys:!1}),c=(0,s.interpolateDynamicPath)(i,t,o),{name:h,ext:p}=n.default.parse(r),m=d(n.default.posix.join(e,h)),f=m?`-${m}`:"";return(0,u.normalizePathSep)(n.default.join(c,`${h}${f}${p}`))}function p(e){if(!(0,i.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=d(e),!t.endsWith("/route")){let{dir:e,name:i,ext:s}=n.default.parse(t);t=n.default.posix.join(e,`${i}${r?`-${r}`:""}${s}`,"route")}return t}function m(e,t){let r=e.endsWith("/route"),i=r?e.slice(0,-6):e,n=i.endsWith("/sitemap")?".xml":"";return(t?`${i}/[__metadata_id__]`:`${i}${n}`)+(r?"/route":"")}},1687:(e,t,r)=>{Promise.resolve().then(r.bind(r,9052)),Promise.resolve().then(r.bind(r,3587)),Promise.resolve().then(r.bind(r,4295)),Promise.resolve().then(r.bind(r,6017)),Promise.resolve().then(r.bind(r,3427)),Promise.resolve().then(r.bind(r,9579)),Promise.resolve().then(r.bind(r,4647))},1756:(e,t,r)=>{"use strict";r.d(t,{n:()=>i});let i="data-"+(0,r(7886).I)("framerAppearId")},1874:(e,t,r)=>{"use strict";r.d(t,{B:()=>u});var i=r(7758),n=r(5444),s=r(7095),a=r(7236);let o=e=>(0,i.q)(0,255,e),l={...n.ai,transform:e=>Math.round(o(e))},u={test:(0,a.$)("rgb","red"),parse:(0,a.q)("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:i=1})=>"rgba("+l.transform(e)+", "+l.transform(t)+", "+l.transform(r)+", "+(0,s.a)(n.X4.transform(i))+")"}},1888:(e,t,r)=>{"use strict";r.d(t,{w:()=>i});let i=e=>t=>t.test(e)},2082:(e,t,r)=>{"use strict";r.d(t,{Q:()=>i});let i={value:null,addProjectionMetrics:null}},2145:(e,t,r)=>{"use strict";r.d(t,{z:()=>o});var i=r(687),n=r(3210),s=r(331),a=r(6360);function o({children:e,containerClassName:t,className:r,as:o="button",duration:l=1,clockwise:u=!0,...c}){let[d,h]=(0,n.useState)(!1),[p,m]=(0,n.useState)("TOP"),f={TOP:"radial-gradient(20.7% 50% at 50% 0%, hsl(0, 0%, 100%) 0%, rgba(255, 255, 255, 0) 100%)",LEFT:"radial-gradient(16.6% 43.1% at 0% 50%, hsl(0, 0%, 100%) 0%, rgba(255, 255, 255, 0) 100%)",BOTTOM:"radial-gradient(20.7% 50% at 50% 100%, hsl(0, 0%, 100%) 0%, rgba(255, 255, 255, 0) 100%)",RIGHT:"radial-gradient(16.2% 41.199999999999996% at 100% 50%, hsl(0, 0%, 100%) 0%, rgba(255, 255, 255, 0) 100%)"};return(0,i.jsxs)(o,{onMouseEnter:e=>{h(!0)},onMouseLeave:()=>h(!1),className:(0,a.cn)("relative flex rounded-full border content-center bg-black/20 hover:bg-black/10 transition duration-500 dark:bg-white/20 items-center flex-col flex-nowrap gap-10 h-min justify-center overflow-visible p-px decoration-clone w-fit",t),...c,children:[(0,i.jsx)("div",{className:(0,a.cn)("w-auto text-white z-10 bg-black px-4 py-2 rounded-[inherit]",r),children:e}),(0,i.jsx)(s.P.div,{className:(0,a.cn)("flex-none inset-0 overflow-hidden absolute z-0 rounded-[inherit]"),style:{filter:"blur(2px)",position:"absolute",width:"100%",height:"100%"},initial:{background:f[p]},animate:{background:d?[f[p],"radial-gradient(75% 181.15942028985506% at 50% 50%, #0ea5e9 0%, rgba(255, 255, 255, 0) 100%)"]:f[p]},transition:{ease:"linear",duration:l??1}}),(0,i.jsx)("div",{className:"bg-black absolute z-1 flex-none inset-[2px] rounded-[100px]"})]})}},2157:(e,t,r)=>{"use strict";r.d(t,{L:()=>i});let i=(0,r(3210).createContext)({})},2238:(e,t,r)=>{"use strict";r.d(t,{j:()=>n,p:()=>a});let i=e=>t=>"string"==typeof t&&t.startsWith(e),n=i("--"),s=i("var(--"),a=e=>!!s(e)&&o.test(e.split("/*")[0].trim()),o=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},2437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return n}});let i=r(5362);function n(e,t){let r=[],n=(0,i.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),s=(0,i.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(n.source),n.flags):n,r);return(e,i)=>{if("string"!=typeof e)return!1;let n=s(e);if(!n)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete n.params[e.name];return{...i,...n.params}}}},2441:(e,t,r)=>{"use strict";r.d(t,{V:()=>i});let i=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2},2485:(e,t,r)=>{"use strict";r.d(t,{OU:()=>u,Ql:()=>c,Ww:()=>h,hq:()=>s,o4:()=>l});var i=r(8028),n=r(7606);function s(e,t,r){return r+t*(e-r)}function a(e,t,r,i,n){return void 0!==n&&(e=i+n*(e-i)),i+r*(e-i)+t}function o(e,t=0,r=1,i,n){e.min=a(e.min,t,r,i,n),e.max=a(e.max,t,r,i,n)}function l(e,{x:t,y:r}){o(e.x,t.translate,t.scale,t.originPoint),o(e.y,r.translate,r.scale,r.originPoint)}function u(e,t,r,i=!1){let s,a,o=r.length;if(o){t.x=t.y=1;for(let u=0;u<o;u++){a=(s=r[u]).projectionDelta;let{visualElement:o}=s.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(i&&s.options.layoutScroll&&s.scroll&&s!==s.root&&h(e,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,l(e,a)),i&&(0,n.HD)(s.latestValues)&&h(e,s.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}function c(e,t){e.min=e.min+t,e.max=e.max+t}function d(e,t,r,n,s=.5){let a=(0,i.k)(e.min,e.max,s);o(e,t,r,a,n)}function h(e,t){d(e.x,t.x,t.scaleX,t.scale,t.originX),d(e.y,t.y,t.scaleY,t.scale,t.originY)}},2572:(e,t,r)=>{"use strict";function i({top:e,left:t,right:r,bottom:i}){return{x:{min:t,max:r},y:{min:e,max:i}}}function n({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function s(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),i=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:i.y,right:i.x}}r.d(t,{FY:()=>i,bS:()=>s,pA:()=>n})},2582:(e,t,r)=>{"use strict";r.d(t,{Q:()=>i});let i=(0,r(3210).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},2688:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var i=r(3210);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),a=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:s="",children:a,iconNode:c,...d},h)=>(0,i.createElement)("svg",{ref:h,...u,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:o("lucide",s),...!a&&!l(d)&&{"aria-hidden":"true"},...d},[...c.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(a)?a:[a]])),d=(e,t)=>{let r=(0,i.forwardRef)(({className:r,...s},l)=>(0,i.createElement)(c,{ref:l,iconNode:t,className:o(`lucide-${n(a(e))}`,`lucide-${e}`,r),...s}));return r.displayName=a(e),r}},2699:(e,t,r)=>{"use strict";r.d(t,{C:()=>i});let i=new WeakMap},2702:(e,t,r)=>{"use strict";r.d(t,{B:()=>o});var i=r(8744),n=r(2874);let s={offset:"stroke-dashoffset",array:"stroke-dasharray"},a={offset:"strokeDashoffset",array:"strokeDasharray"};function o(e,{attrX:t,attrY:r,attrScale:o,pathLength:l,pathSpacing:u=1,pathOffset:c=0,...d},h,p,m){if((0,i.O)(e,d,p),h){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:f,style:g}=e;f.transform&&(g.transform=f.transform,delete f.transform),(g.transform||f.transformOrigin)&&(g.transformOrigin=f.transformOrigin??"50% 50%",delete f.transformOrigin),g.transform&&(g.transformBox=m?.transformBox??"fill-box",delete f.transformBox),void 0!==t&&(f.x=t),void 0!==r&&(f.y=r),void 0!==o&&(f.scale=o),void 0!==l&&function(e,t,r=1,i=0,o=!0){e.pathLength=1;let l=o?s:a;e[l.offset]=n.px.transform(-i);let u=n.px.transform(t),c=n.px.transform(r);e[l.array]=`${u} ${c}`}(f,l,u,c,!1)}},2716:(e,t,r)=>{"use strict";r.d(t,{po:()=>s,tn:()=>o,yT:()=>a});var i=r(2441),n=r(8830);let s=e=>1-Math.sin(Math.acos(e)),a=(0,n.G)(s),o=(0,i.V)(s)},2742:(e,t,r)=>{"use strict";r.d(t,{V:()=>o});var i=r(5444),n=r(2874),s=r(7095),a=r(7236);let o={test:(0,a.$)("hsl","hue"),parse:(0,a.q)("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:a=1})=>"hsla("+Math.round(e)+", "+n.KN.transform((0,s.a)(t))+", "+n.KN.transform((0,s.a)(r))+", "+(0,s.a)(i.X4.transform(a))+")"}},2743:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var i=r(3210);let n=r(7044).B?i.useLayoutEffect:i.useEffect},2750:(e,t,r)=>{"use strict";r.d(t,{Contact:()=>f});var i=r(687),n=r(3210),s=r(331),a=r(6360),o=r(8340),l=r(1550),u=r(7992),c=r(2688);let d=(0,c.A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),h=(0,c.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),p=(0,c.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),m=(0,c.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);function f(){let[e,t]=(0,n.useState)({name:"",email:"",phone:"",vehicle:"",service:"",message:""}),[r,c]=(0,n.useState)(!1),f=r=>{t({...e,[r.target.name]:r.target.value})},g=[{icon:(0,i.jsx)(o.A,{className:"w-6 h-6"}),title:"Phone",details:"(*************",subtitle:"Call or text anytime"},{icon:(0,i.jsx)(l.A,{className:"w-6 h-6"}),title:"Email",details:"<EMAIL>",subtitle:"Quick response guaranteed"},{icon:(0,i.jsx)(u.A,{className:"w-6 h-6"}),title:"Service Area",details:"Four Corners Region",subtitle:"Mobile service available"},{icon:(0,i.jsx)(d,{className:"w-6 h-6"}),title:"Hours",details:"Mon-Sat: 8AM-6PM",subtitle:"Sunday by appointment"}];return(0,i.jsxs)("section",{id:"contact",className:"py-20 bg-gradient-to-b from-white to-neutral-50 relative overflow-hidden",children:[(0,i.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-yellow-500 to-blue-500"}),(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsxs)(s.P.div,{...a.X1,className:"text-center mb-16",children:[(0,i.jsx)(s.P.span,{...a.X1,className:"inline-block px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-semibold mb-4",children:"Get In Touch"}),(0,i.jsxs)(s.P.h2,{...a.X1,className:"text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-800 mb-6",children:["Ready to Transform",(0,i.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-yellow-600 bg-clip-text text-transparent",children:" Your Vehicle?"})]}),(0,i.jsx)(s.P.p,{...a.X1,className:"text-lg md:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed",children:"Get your free quote today and discover why vehicle owners across the Four Corners region trust us with their most valuable investments."})]}),(0,i.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto",children:[(0,i.jsxs)(s.P.div,{...a.X1,className:"bg-white rounded-3xl p-8 shadow-xl border border-neutral-100",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-yellow-500 rounded-full flex items-center justify-center",children:(0,i.jsx)(h,{className:"w-6 h-6 text-white"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-neutral-800",children:"Get Your Free Quote"}),(0,i.jsx)("p",{className:"text-neutral-600",children:"We'll respond within 24 hours"})]})]}),(0,i.jsxs)("form",{onSubmit:e=>{e.preventDefault(),c(!0),setTimeout(()=>c(!1),3e3)},className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-semibold text-neutral-700 mb-2",children:"Full Name *"}),(0,i.jsx)("input",{type:"text",name:"name",value:e.name,onChange:f,required:!0,className:"w-full px-4 py-3 border border-neutral-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",placeholder:"Your name"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-semibold text-neutral-700 mb-2",children:"Phone Number *"}),(0,i.jsx)("input",{type:"tel",name:"phone",value:e.phone,onChange:f,required:!0,className:"w-full px-4 py-3 border border-neutral-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",placeholder:"(*************"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-semibold text-neutral-700 mb-2",children:"Email Address *"}),(0,i.jsx)("input",{type:"email",name:"email",value:e.email,onChange:f,required:!0,className:"w-full px-4 py-3 border border-neutral-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",placeholder:"<EMAIL>"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-semibold text-neutral-700 mb-2",children:"Vehicle Information"}),(0,i.jsx)("input",{type:"text",name:"vehicle",value:e.vehicle,onChange:f,className:"w-full px-4 py-3 border border-neutral-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",placeholder:"Year, Make, Model (e.g., 2023 Tesla Model S)"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-semibold text-neutral-700 mb-2",children:"Service Needed"}),(0,i.jsxs)("select",{name:"service",value:e.service,onChange:f,className:"w-full px-4 py-3 border border-neutral-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",children:[(0,i.jsx)("option",{value:"",children:"Select a service"}),["Ceramic Coating","Paint Correction","Interior Detail","Exterior Wash & Wax","Mobile Detailing","Full Vehicle Restoration","Other (specify in message)"].map(e=>(0,i.jsx)("option",{value:e,children:e},e))]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-semibold text-neutral-700 mb-2",children:"Additional Details"}),(0,i.jsx)("textarea",{name:"message",value:e.message,onChange:f,rows:4,className:"w-full px-4 py-3 border border-neutral-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none",placeholder:"Tell us about your vehicle's condition, specific concerns, or any questions you have..."})]}),(0,i.jsx)(s.P.button,{type:"submit",whileHover:{scale:1.02},whileTap:{scale:.98},disabled:r,className:"w-full bg-gradient-to-r from-blue-600 to-yellow-600 text-white py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50",children:r?(0,i.jsxs)("span",{className:"flex items-center justify-center gap-2",children:[(0,i.jsx)(p,{className:"w-5 h-5"}),"Message Sent!"]}):(0,i.jsxs)("span",{className:"flex items-center justify-center gap-2",children:[(0,i.jsx)(h,{className:"w-5 h-5"}),"Get My Free Quote"]})})]})]}),(0,i.jsxs)(s.P.div,{...a.X1,className:"space-y-8",children:[(0,i.jsx)("div",{className:"grid gap-6",children:g.map((e,t)=>(0,i.jsx)(s.P.div,{...(0,a.F0)(.1*t),className:"bg-white rounded-2xl p-6 shadow-lg border border-neutral-100 hover:shadow-xl transition-all duration-300",children:(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-yellow-500 rounded-full flex items-center justify-center text-white",children:e.icon}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold text-neutral-800 text-lg",children:e.title}),(0,i.jsx)("p",{className:"text-neutral-900 font-medium",children:e.details}),(0,i.jsx)("p",{className:"text-sm text-neutral-600",children:e.subtitle})]})]})},t))}),(0,i.jsxs)(s.P.div,{...a.X1,className:"bg-gradient-to-r from-blue-50 to-yellow-50 rounded-2xl p-6",children:[(0,i.jsx)("h4",{className:"font-bold text-neutral-800 text-lg mb-4",children:"Quick Actions"}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)(s.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full bg-white text-neutral-800 py-3 rounded-xl font-semibold flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-300",children:[(0,i.jsx)(o.A,{className:"w-5 h-5"}),"Call Now"]}),(0,i.jsxs)(s.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full bg-white text-neutral-800 py-3 rounded-xl font-semibold flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-300",children:[(0,i.jsx)(m,{className:"w-5 h-5"}),"Schedule Online"]})]})]})]})]})]})]})}},2769:(e,t,r)=>{"use strict";r.d(t,{o:()=>f});var i=r(7758),n=r(7211),s=r(8347),a=r(4948),o=r(7690),l=r(1062);let u={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};var c=r(6244);function d(e,t){return e*Math.sqrt(1-t*t)}let h=["duration","bounce"],p=["stiffness","damping","mass"];function m(e,t){return t.some(t=>void 0!==e[t])}function f(e=u.visualDuration,t=u.bounce){let r,o="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:g,restDelta:x}=o,v=o.keyframes[0],y=o.keyframes[o.keyframes.length-1],b={done:!1,value:v},{stiffness:w,damping:j,mass:P,duration:k,velocity:A,isResolvedFromDuration:T}=function(e){let t={velocity:u.velocity,stiffness:u.stiffness,damping:u.damping,mass:u.mass,isResolvedFromDuration:!1,...e};if(!m(e,p)&&m(e,h))if(e.visualDuration){let r=2*Math.PI/(1.2*e.visualDuration),n=r*r,s=2*(0,i.q)(.05,1,1-(e.bounce||0))*Math.sqrt(n);t={...t,mass:u.mass,stiffness:n,damping:s}}else{let r=function({duration:e=u.duration,bounce:t=u.bounce,velocity:r=u.velocity,mass:s=u.mass}){let a,o;(0,c.$)(e<=(0,n.f)(u.maxDuration),"Spring duration must be 10 seconds or less");let l=1-t;l=(0,i.q)(u.minDamping,u.maxDamping,l),e=(0,i.q)(u.minDuration,u.maxDuration,(0,n.X)(e)),l<1?(a=t=>{let i=t*l,n=i*e;return .001-(i-r)/d(t,l)*Math.exp(-n)},o=t=>{let i=t*l*e,n=Math.pow(l,2)*Math.pow(t,2)*e,s=Math.exp(-i),o=d(Math.pow(t,2),l);return(i*r+r-n)*s*(-a(t)+.001>0?-1:1)/o}):(a=t=>-.001+Math.exp(-t*e)*((t-r)*e+1),o=t=>e*e*(r-t)*Math.exp(-t*e));let h=function(e,t,r){let i=r;for(let r=1;r<12;r++)i-=e(i)/t(i);return i}(a,o,5/e);if(e=(0,n.f)(e),isNaN(h))return{stiffness:u.stiffness,damping:u.damping,duration:e};{let t=Math.pow(h,2)*s;return{stiffness:t,damping:2*l*Math.sqrt(s*t),duration:e}}}(e);(t={...t,...r,mass:u.mass}).isResolvedFromDuration=!0}return t}({...o,velocity:-(0,n.X)(o.velocity||0)}),E=A||0,N=j/(2*Math.sqrt(w*P)),S=y-v,C=(0,n.X)(Math.sqrt(w/P)),M=5>Math.abs(S);if(g||(g=M?u.restSpeed.granular:u.restSpeed.default),x||(x=M?u.restDelta.granular:u.restDelta.default),N<1){let e=d(C,N);r=t=>y-Math.exp(-N*C*t)*((E+N*C*S)/e*Math.sin(e*t)+S*Math.cos(e*t))}else if(1===N)r=e=>y-Math.exp(-C*e)*(S+(E+C*S)*e);else{let e=C*Math.sqrt(N*N-1);r=t=>{let r=Math.exp(-N*C*t),i=Math.min(e*t,300);return y-r*((E+N*C*S)*Math.sinh(i)+e*S*Math.cosh(i))/e}}let R={calculatedDuration:T&&k||null,next:e=>{let t=r(e);if(T)b.done=e>=k;else{let i=0===e?E:0;N<1&&(i=0===e?(0,n.f)(E):(0,l.Y)(r,e,t));let s=Math.abs(y-t)<=x;b.done=Math.abs(i)<=g&&s}return b.value=b.done?y:t,b},toString:()=>{let e=Math.min((0,a.t)(R),a.Y),t=(0,s.K)(t=>R.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return R}f.applyToOptions=e=>{let t=(0,o.X)(e,100,f);return e.ease=t.ease,e.duration=(0,n.f)(t.duration),e.type="keyframes",e}},2785:(e,t)=>{"use strict";function r(e){let t={};for(let[r,i]of e.entries()){let e=t[r];void 0===e?t[r]=i:Array.isArray(e)?e.push(i):t[r]=[e,i]}return t}function i(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function n(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,i(e));else t.set(r,i(n));return t}function s(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,i]of t.entries())e.append(r,i)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return s},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return n}})},2789:(e,t,r)=>{"use strict";r.d(t,{M:()=>n});var i=r(3210);function n(e){let t=(0,i.useRef)(null);return null===t.current&&(t.current=e()),t.current}},2874:(e,t,r)=>{"use strict";r.d(t,{KN:()=>s,gQ:()=>u,px:()=>a,uj:()=>n,vh:()=>o,vw:()=>l});let i=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),n=i("deg"),s=i("%"),a=i("px"),o=i("vh"),l=i("vw"),u={...s,parse:e=>s.parse(e)/100,transform:e=>s.transform(100*e)}},2923:(e,t,r)=>{"use strict";function i(e,t){return e?.[t]??e?.default??e}r.d(t,{r:()=>i})},2953:(e,t,r)=>{"use strict";r.d(t,{L:()=>a,m:()=>s});var i=r(2572),n=r(2485);function s(e,t){return(0,i.FY)((0,i.bS)(e.getBoundingClientRect(),t))}function a(e,t,r){let i=s(e,r),{scroll:a}=t;return a&&((0,n.Ql)(i.x,a.offset.x),(0,n.Ql)(i.y,a.offset.y)),i}},2958:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3063:(e,t,r)=>{"use strict";r.d(t,{u:()=>n});var i=r(1874);let n={test:(0,r(7236).$)("#"),parse:function(e){let t="",r="",i="",n="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),i=e.substring(5,7),n=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),i=e.substring(3,4),n=e.substring(4,5),t+=t,r+=r,i+=i,n+=n),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(i,16),alpha:n?parseInt(n,16)/255:1}},transform:i.B.transform}},3088:(e,t,r)=>{"use strict";function i(e,{style:t,vars:r},i,n){for(let s in Object.assign(e.style,t,n&&n.getProjectionStyles(i)),r)e.style.setProperty(s,r[s])}r.d(t,{e:()=>i})},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return n}});let r=/[|\\{}()[\]^$+*?.-]/,i=/[|\\{}()[\]^$+*?.-]/g;function n(e){return r.test(e)?e.replace(i,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3361:(e,t,r)=>{"use strict";r.d(t,{l:()=>i});let i=e=>e},3427:(e,t,r)=>{"use strict";r.d(t,{Hero:()=>i});let i=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Hero() from the server but Hero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\sections\\hero.tsx","Hero")},3587:(e,t,r)=>{"use strict";r.d(t,{Navbar:()=>i});let i=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\navigation\\navbar.tsx","Navbar")},3671:(e,t,r)=>{"use strict";r.d(t,{Gt:()=>n,PP:()=>o,WG:()=>s,uv:()=>a});var i=r(3361);let{schedule:n,cancel:s,state:a,steps:o}=(0,r(9848).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:i.l,!0)},3677:(e,t,r)=>{"use strict";r.d(t,{Testimonials:()=>l});var i=r(687),n=r(331),s=r(6360);let a=(0,r(2688).A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]]);var o=r(4398);function l(){return(0,i.jsxs)("section",{className:"py-20 bg-gradient-to-b from-white to-neutral-50 relative overflow-hidden",children:[(0,i.jsx)("div",{className:"absolute top-0 right-0 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl"}),(0,i.jsx)("div",{className:"absolute bottom-0 left-0 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"}),(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsxs)(n.P.div,{...s.X1,className:"text-center mb-16",children:[(0,i.jsx)(n.P.span,{...s.X1,className:"inline-block px-4 py-2 bg-yellow-100 text-yellow-800 rounded-full text-sm font-semibold mb-4",children:"Client Testimonials"}),(0,i.jsxs)(n.P.h2,{...s.X1,className:"text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-800 mb-6",children:["What Our Clients",(0,i.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-yellow-600 bg-clip-text text-transparent",children:" Say"})]}),(0,i.jsx)(n.P.p,{...s.X1,className:"text-lg md:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed",children:"Don't just take our word for it. Here's what vehicle owners across the Four Corners region say about our premium detailing services."})]}),(0,i.jsx)(n.P.div,{...s.X1,className:"grid grid-cols-2 md:grid-cols-4 gap-8 mb-16",children:[{number:"500+",label:"Vehicles Detailed"},{number:"5.0",label:"Average Rating"},{number:"100%",label:"Customer Satisfaction"},{number:"3+",label:"Years Experience"}].map((e,t)=>(0,i.jsxs)(n.P.div,{...(0,s.F0)(.1*t),className:"text-center",children:[(0,i.jsx)("div",{className:"text-3xl md:text-4xl font-bold text-neutral-800 mb-2",children:e.number}),(0,i.jsx)("div",{className:"text-sm md:text-base text-neutral-600",children:e.label})]},t))}),(0,i.jsx)(n.P.div,{...s.X1,className:"grid md:grid-cols-2 gap-8 max-w-6xl mx-auto",children:[{name:"Sarah Johnson",role:"Tesla Model S Owner",content:"Jon's attention to detail is absolutely incredible. My Tesla looks better than the day I bought it. The ceramic coating has been a game-changer for maintenance.",rating:5,image:"/api/placeholder/60/60"},{name:"Michael Chen",role:"BMW M3 Enthusiast",content:"I've tried many detailing services, but Jon's four-corner approach is unmatched. The paint correction work was flawless, and the results speak for themselves.",rating:5,image:"/api/placeholder/60/60"},{name:"Lisa Rodriguez",role:"Luxury Car Collector",content:"Professional, reliable, and the quality is consistently outstanding. Jon treats every vehicle like it's his own. Highly recommend for anyone who values their investment.",rating:5,image:"/api/placeholder/60/60"},{name:"David Thompson",role:"Mercedes-Benz Owner",content:"The mobile service is incredibly convenient, and the results are always perfect. Jon's expertise and premium products make all the difference.",rating:5,image:"/api/placeholder/60/60"}].map((e,t)=>(0,i.jsxs)(n.P.div,{...(0,s.F0)(.1*t),className:"bg-white rounded-2xl p-8 shadow-lg border border-neutral-100 relative group hover:shadow-xl transition-all duration-300",children:[(0,i.jsx)("div",{className:"absolute top-6 right-6 text-blue-200 group-hover:text-blue-300 transition-colors duration-300",children:(0,i.jsx)(a,{className:"w-8 h-8"})}),(0,i.jsx)("div",{className:"flex items-center gap-1 mb-4",children:[...Array(e.rating)].map((e,t)=>(0,i.jsx)(o.A,{className:"w-5 h-5 fill-yellow-400 text-yellow-400"},t))}),(0,i.jsxs)("p",{className:"text-neutral-700 leading-relaxed mb-6 text-lg",children:['"',e.content,'"']}),(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-yellow-500 rounded-full flex items-center justify-center text-white font-semibold",children:e.name.split(" ").map(e=>e[0]).join("")}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-semibold text-neutral-800",children:e.name}),(0,i.jsx)("div",{className:"text-sm text-neutral-600",children:e.role})]})]})]},t))}),(0,i.jsx)(n.P.div,{...s.X1,className:"text-center mt-16",children:(0,i.jsxs)(n.P.div,{...s.X1,className:"bg-gradient-to-r from-blue-50 to-yellow-50 rounded-3xl p-8 md:p-12 max-w-4xl mx-auto",children:[(0,i.jsx)("h3",{className:"text-2xl md:text-3xl font-bold text-neutral-800 mb-4",children:"Ready to Join Our Satisfied Clients?"}),(0,i.jsx)("p",{className:"text-lg text-neutral-600 mb-8 max-w-2xl mx-auto",children:"Experience the difference that professional, artisan-level detailing can make for your vehicle. Get your free quote today."}),(0,i.jsx)(n.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-gradient-to-r from-blue-600 to-yellow-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300",children:"Get Your Free Quote"})]})})]})]})}},3685:(e,t,r)=>{"use strict";r.d(t,{b:()=>n});var i=r(912);let n=e=>(e*=2)<1?.5*(0,i.dg)(e):.5*(2-Math.pow(2,-10*(e-1)))},3736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return n}}),r(4827);let i=r(2785);function n(e,t,r){void 0===r&&(r=!0);let n=new URL("http://n"),s=t?new URL(t,n):e.startsWith(".")?new URL("http://n"):n,{pathname:a,searchParams:o,search:l,hash:u,href:c,origin:d}=new URL(e,s);if(d!==n.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:r?(0,i.searchParamsToUrlQuery)(o):void 0,search:l,hash:u,href:c.slice(d.length)}}},3873:e=>{"use strict";e.exports=require("path")},3905:(e,t,r)=>{"use strict";r.d(t,{h:()=>n});var i=r(4156);function n(e){return(0,i.x)(e)&&"svg"===e.tagName}},4068:(e,t,r)=>{"use strict";r.d(t,{q:()=>i});let i=(e,t,r)=>{let i=t-e;return 0===i?1:(r-e)/i}},4156:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var i=r(4479);function n(e){return(0,i.G)(e)&&"ownerSVGElement"in e}},4177:(e,t,r)=>{"use strict";r.d(t,{D:()=>i});let i=e=>Array.isArray(e)&&"number"==typeof e[0]},4278:(e,t,r)=>{"use strict";r.d(t,{i:()=>i});let i=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e)},4295:(e,t,r)=>{"use strict";r.d(t,{Contact:()=>i});let i=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Contact() from the server but Contact is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\sections\\contact.tsx","Contact")},4296:(e,t,r)=>{"use strict";r.d(t,{v:()=>n});var i=r(7556);class n{constructor(){this.subscriptions=[]}add(e){return(0,i.Kq)(this.subscriptions,e),()=>(0,i.Ai)(this.subscriptions,e)}notify(e,t,r){let i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](e,t,r);else for(let n=0;n<i;n++){let i=this.subscriptions[n];i&&i(e,t,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},4325:(e,t,r)=>{"use strict";let i;r.d(t,{k:()=>o});var n=r(7819),s=r(3671);function a(){i=void 0}let o={now:()=>(void 0===i&&o.set(s.uv.isProcessing||n.W.useManualTiming?s.uv.timestamp:performance.now()),i),set:e=>{i=e,queueMicrotask(a)}}},4342:(e,t,r)=>{"use strict";r.d(t,{OQ:()=>c});var i=r(4296),n=r(5547),s=r(4325),a=r(3671);let o=e=>!isNaN(parseFloat(e)),l={current:void 0};class u{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let r=s.k.now();if(this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=s.k.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=o(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new i.v);let r=this.events[e].add(t);return"change"===e?()=>{r(),a.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-r}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let e=s.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let t=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,n.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),t)}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function c(e,t){return new u(e,t)}},4387:()=>{},4396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return f},getNamedRouteRegex:function(){return m},getRouteRegex:function(){return d},parseParameter:function(){return l}});let i=r(6143),n=r(1437),s=r(3293),a=r(2887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(o);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function c(e,t,r){let i={},l=1,c=[];for(let d of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=n.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),a=d.match(o);if(e&&a&&a[2]){let{key:t,optional:r,repeat:n}=u(a[2]);i[t]={pos:l++,repeat:n,optional:r},c.push("/"+(0,s.escapeStringRegexp)(e)+"([^/]+?)")}else if(a&&a[2]){let{key:e,repeat:t,optional:n}=u(a[2]);i[e]={pos:l++,repeat:t,optional:n},r&&a[1]&&c.push("/"+(0,s.escapeStringRegexp)(a[1]));let o=t?n?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&a[1]&&(o=o.substring(1)),c.push(o)}else c.push("/"+(0,s.escapeStringRegexp)(d));t&&a&&a[3]&&c.push((0,s.escapeStringRegexp)(a[3]))}return{parameterizedRoute:c.join(""),groups:i}}function d(e,t){let{includeSuffix:r=!1,includePrefix:i=!1,excludeOptionalTrailingSlash:n=!1}=void 0===t?{}:t,{parameterizedRoute:s,groups:a}=c(e,r,i),o=s;return n||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:a}}function h(e){let t,{interceptionMarker:r,getSafeRouteKey:i,segment:n,routeKeys:a,keyPrefix:o,backreferenceDuplicateKeys:l}=e,{key:c,optional:d,repeat:h}=u(n),p=c.replace(/\W/g,"");o&&(p=""+o+p);let m=!1;(0===p.length||p.length>30)&&(m=!0),isNaN(parseInt(p.slice(0,1)))||(m=!0),m&&(p=i());let f=p in a;o?a[p]=""+o+c:a[p]=c;let g=r?(0,s.escapeStringRegexp)(r):"";return t=f&&l?"\\k<"+p+">":h?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",d?"(?:/"+g+t+")?":"/"+g+t}function p(e,t,r,l,u){let c,d=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},m=[];for(let c of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=n.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),a=c.match(o);if(e&&a&&a[2])m.push(h({getSafeRouteKey:d,interceptionMarker:a[1],segment:a[2],routeKeys:p,keyPrefix:t?i.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(a&&a[2]){l&&a[1]&&m.push("/"+(0,s.escapeStringRegexp)(a[1]));let e=h({getSafeRouteKey:d,segment:a[2],routeKeys:p,keyPrefix:t?i.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&a[1]&&(e=e.substring(1)),m.push(e)}else m.push("/"+(0,s.escapeStringRegexp)(c));r&&a&&a[3]&&m.push((0,s.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:m.join(""),routeKeys:p}}function m(e,t){var r,i,n;let s=p(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(i=t.includePrefix)&&i,null!=(n=t.backreferenceDuplicateKeys)&&n),a=s.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...d(e,t),namedRegex:"^"+a+"$",routeKeys:s.routeKeys}}function f(e,t){let{parameterizedRoute:r}=c(e,!1,!1),{catchAll:i=!0}=t;if("/"===r)return{namedRegex:"^/"+(i?".*":"")+"$"};let{namedParameterizedRoute:n}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+n+(i?"(?:(/.*)?)":"")+"$"}}},4398:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(2688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>l});var i=r(7413),n=r(5091),s=r.n(n),a=r(1386),o=r.n(a);r(1135);let l={title:"Jon Four Corners Detailing | Premium Automotive Detailing Services",description:"Professional automotive detailing and reconditioning services. Specializing in ceramic coating, paint correction, and mobile detailing. Protecting your investment with artisan-level craftsmanship.",keywords:["automotive detailing","car detailing","ceramic coating","paint correction","mobile detailing","Four Corners","premium car care"],authors:[{name:"Jon Four Corners Detailing"}],openGraph:{title:"Jon Four Corners Detailing | Premium Automotive Detailing",description:"Professional automotive detailing services with artisan-level craftsmanship. Ceramic coating, paint correction, and mobile detailing.",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"Jon Four Corners Detailing | Premium Automotive Detailing",description:"Professional automotive detailing services with artisan-level craftsmanship."},robots:{index:!0,follow:!0}};function u({children:e}){return(0,i.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,i.jsx)("body",{className:`${s().variable} ${o().variable} antialiased min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100`,children:(0,i.jsx)("div",{className:"relative",children:e})})})}},4479:(e,t,r)=>{"use strict";function i(e){return"object"==typeof e&&null!==e}r.d(t,{G:()=>i})},4538:(e,t,r)=>{"use strict";r.d(t,{ge:()=>a,xU:()=>n});let i=()=>({translate:0,scale:1,origin:0,originPoint:0}),n=()=>({x:i(),y:i()}),s=()=>({min:0,max:0}),a=()=>({x:s(),y:s()})},4647:(e,t,r)=>{"use strict";r.d(t,{Testimonials:()=>i});let i=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Testimonials() from the server but Testimonials is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\sections\\testimonials.tsx","Testimonials")},4693:(e,t,r)=>{"use strict";r.d(t,{K:()=>n});var i=r(8337);function n(e,t,r){let n=e.getProps();return(0,i.a)(n,t,void 0!==r?r:n.custom,e)}},4722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return s},normalizeRscURL:function(){return a}});let i=r(5531),n=r(5499);function s(e){return(0,i.ensureLeadingSlash)(e.split("/").reduce((e,t,r,i)=>!t||(0,n.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===i.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},4799:(e,t,r)=>{"use strict";r.d(t,{a6:()=>n,am:()=>a,vT:()=>s});var i=r(1455);let n=(0,i.A)(.42,0,1,1),s=(0,i.A)(0,0,.58,1),a=(0,i.A)(.42,0,.58,1)},4819:(e,t,r)=>{"use strict";r.d(t,{K:()=>p});var i=r(6244),n=r(3361),s=r(3685),a=r(912),o=r(2716),l=r(1455),u=r(4799),c=r(4177);let d={linear:n.l,easeIn:u.a6,easeInOut:u.am,easeOut:u.vT,circIn:o.po,circInOut:o.tn,circOut:o.yT,backIn:a.dg,backInOut:a.ZZ,backOut:a.Sz,anticipate:s.b},h=e=>"string"==typeof e,p=e=>{if((0,c.D)(e)){(0,i.V)(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,r,n,s]=e;return(0,l.A)(t,r,n,s)}return h(e)?((0,i.V)(void 0!==d[e],`Invalid easing type '${e}'`),d[e]):e}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return x},NormalizeError:function(){return f},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return i},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return o},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return y}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function i(e){let t,r=!1;return function(){for(var i=arguments.length,n=Array(i),s=0;s<i;s++)n[s]=arguments[s];return r||(r=!0,t=e(...n)),t}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=e=>n.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function o(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let i=await e.getInitialProps(t);if(r&&u(r))return i;if(!i)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+i+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i}let h="undefined"!=typeof performance,p=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class f extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class x extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function y(e){return JSON.stringify({message:e.message,stack:e.stack})}},4832:(e,t,r)=>{"use strict";r.d(t,{Hero:()=>et});var i=r(687),n=r(331),s=r(3210),a=r(2789);class o{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>e.finished))}getAll(e){return this.animations[0][e]}setAll(e,t){for(let r=0;r<this.animations.length;r++)this.animations[r][e]=t}attachTimeline(e){let t=this.animations.map(t=>t.attachTimeline(e));return()=>{t.forEach((e,t)=>{e&&e(),this.animations[t].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}runAll(e){this.animations.forEach(t=>t[e]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class l extends o{then(e,t){return this.finished.finally(e).then(()=>{})}}var u=r(2769),c=r(8267),d=r(1008),h=r(7690),p=r(5723),m=r(5927),f=r(7211),g=r(6244);let x=(e,t,r)=>{let i=t-e;return((r-e)%i+i)%i+e};var v=r(9527);function y(e,t){return(0,v.h)(e)?e[x(0,e.length,t)]:e}var b=r(4068),w=r(9292);function j(e){return"object"==typeof e&&!Array.isArray(e)}function P(e,t,r,i){return"string"==typeof e&&j(t)?(0,w.K)(e,r,i):e instanceof NodeList?Array.from(e):Array.isArray(e)?e:[e]}function k(e,t,r,i){return"number"==typeof t?t:t.startsWith("-")||t.startsWith("+")?Math.max(0,e+parseFloat(t)):"<"===t?r:t.startsWith("<")?Math.max(0,r+parseFloat(t.slice(1))):i.get(t)??e}var A=r(8028),T=r(7556);function E(e,t){return e.at!==t.at?e.at-t.at:null===e.value?1:null===t.value?-1:0}function N(e,t){return t.has(e)||t.set(e,{}),t.get(e)}function S(e,t){return t[e]||(t[e]=[]),t[e]}let C=e=>"number"==typeof e,M=e=>e.every(C);var R=r(2699),D=r(1565),V=r(4156),O=r(3905),_=r(515),F=r(4538),L=r(9542);class I extends L.B{constructor(){super(...arguments),this.type="object"}readValueFromInstance(e,t){if(t in e){let r=e[t];if("string"==typeof r||"number"==typeof r)return r}}getBaseTargetFromProps(){}removeValueFromRenderState(e,t){delete t.output[e]}measureInstanceViewportBox(){return(0,F.ge)()}build(e,t){Object.assign(e.output,t)}renderInstance(e,{output:t}){Object.assign(e,t)}sortInstanceNodePosition(){return 0}}var z=r(8778);function $(e){let t={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},r=(0,V.x)(e)&&!(0,O.h)(e)?new z.l(t):new _.M(t);r.mount(e),R.C.set(e,r)}function B(e){let t=new I({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});t.mount(e),R.C.set(e,t)}var U=r(5944);function W(e,t,r,i){let n=[];if((0,m.S)(e)||"number"==typeof e||"string"==typeof e&&!j(t))n.push((0,U.z)(e,j(t)&&t.default||t,r&&r.default||r));else{let s=P(e,t,i),a=s.length;(0,g.V)(!!a,"No valid elements provided.");for(let e=0;e<a;e++){let i=s[e],o=i instanceof Element?$:B;R.C.has(i)||o(i);let l=R.C.get(i),u={...r};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(e,a)),n.push(...(0,D.$)(l,{...t,transition:u},{}))}}return n}function q(e){return function(t,r,i){let n=[],s=new l(n=Array.isArray(t)&&t.some(Array.isArray)?function(e,t,r){let i=[];return(function(e,{defaultTransition:t={},...r}={},i,n){let s=t.duration||.3,a=new Map,o=new Map,l={},u=new Map,x=0,v=0,w=0;for(let r=0;r<e.length;r++){let a=e[r];if("string"==typeof a){u.set(a,v);continue}if(!Array.isArray(a)){u.set(a.name,k(v,a.at,x,u));continue}let[b,E,R={}]=a;void 0!==R.at&&(v=k(v,R.at,x,u));let D=0,V=(e,r,i,a=0,o=0)=>{var l;let u=Array.isArray(l=e)?l:[l],{delay:m=0,times:x=(0,c.Z)(u),type:b="keyframes",repeat:j,repeatType:P,repeatDelay:k=0,...E}=r,{ease:N=t.ease||"easeOut",duration:S}=r,C="function"==typeof m?m(a,o):m,R=u.length,V=(0,d.W)(b)?b:n?.[b||"keyframes"];if(R<=2&&V){let e=100;2===R&&M(u)&&(e=Math.abs(u[1]-u[0]));let t={...E};void 0!==S&&(t.duration=(0,f.f)(S));let r=(0,h.X)(t,e,V);N=r.ease,S=r.duration}S??(S=s);let O=v+C;1===x.length&&0===x[0]&&(x[1]=1);let _=x.length-u.length;if(_>0&&(0,p.f)(x,_),1===u.length&&u.unshift(null),j){(0,g.V)(j<20,"Repeat count too high, must be less than 20");S*=j+1;let e=[...u],t=[...x],r=[...N=Array.isArray(N)?[...N]:[N]];for(let i=0;i<j;i++){u.push(...e);for(let n=0;n<e.length;n++)x.push(t[n]+(i+1)),N.push(0===n?"linear":y(r,n-1))}for(let e=0;e<x.length;e++)x[e]=x[e]/(j+1)}let F=O+S;!function(e,t,r,i,n,s){for(let t=0;t<e.length;t++){let r=e[t];r.at>n&&r.at<s&&((0,T.Ai)(e,r),t--)}for(let a=0;a<t.length;a++)e.push({value:t[a],at:(0,A.k)(n,s,i[a]),easing:y(r,a)})}(i,u,N,x,O,F),D=Math.max(C+S,D),w=Math.max(F,w)};if((0,m.S)(b))V(E,R,S("default",N(b,o)));else{let e=P(b,E,i,l),t=e.length;for(let r=0;r<t;r++){let i=N(e[r],o);for(let e in E){var j,C;V(E[e],(j=R,C=e,j&&j[C]?{...j,...j[C]}:{...j}),S(e,i),r,t)}}}x=v,v+=D}return o.forEach((e,i)=>{for(let n in e){let s=e[n];s.sort(E);let o=[],l=[],u=[];for(let e=0;e<s.length;e++){let{at:t,value:r,easing:i}=s[e];o.push(r),l.push((0,b.q)(0,w,t)),u.push(i||"easeOut")}0!==l[0]&&(l.unshift(0),o.unshift(o[0]),u.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),o.push(null)),a.has(i)||a.set(i,{keyframes:{},transition:{}});let c=a.get(i);c.keyframes[n]=o,c.transition[n]={...t,duration:w,ease:u,times:l,...r}}}),a})(e,t,r,{spring:u.o}).forEach(({keyframes:e,transition:t},r)=>{i.push(...W(r,e,t))}),i}(t,r,e):W(t,r,i,e));return e&&e.animations.push(s),s}}q();var X=r(4819),G=r(6360);let H=({words:e,className:t,filter:r=!0,duration:o=.5})=>{let[l,u]=function(){var e;let t=(0,a.M)(()=>({current:null,animations:[]})),r=(0,a.M)(()=>q(t));return e=()=>{t.animations.forEach(e=>e.stop())},(0,s.useEffect)(()=>()=>e(),[]),[t,r]}(),c=e.split(" ");return(0,s.useEffect)(()=>{u("span",{opacity:1,filter:r?"blur(0px)":"none"},{duration:o||1,delay:function(e=.1,{startDelay:t=0,from:r=0,ease:i}={}){return(n,s)=>{let a=e*Math.abs(("number"==typeof r?r:function(e,t){if("first"===e)return 0;{let r=t-1;return"last"===e?r:r/2}}(r,s))-n);if(i){let t=s*e;a=(0,X.K)(i)(a/t)*t}return t+a}}(.2)})},[l.current]),(0,i.jsx)("div",{className:(0,G.cn)("font-bold",t),children:(0,i.jsx)("div",{className:"mt-4",children:(0,i.jsx)("div",{className:"dark:text-white text-black text-2xl leading-snug tracking-wide",children:(0,i.jsx)(n.P.div,{ref:l,children:c.map((e,t)=>(0,i.jsxs)(n.P.span,{className:"dark:text-white text-black opacity-0",style:{filter:r?"blur(10px)":"none"},children:[e," "]},e+t))})})})})},K=({children:e,className:t,containerClassName:r,animate:s=!0})=>{let a={initial:{backgroundPosition:"0 50%"},animate:{backgroundPosition:["0, 50%","100% 50%","0 50%"]}};return(0,i.jsxs)("div",{className:(0,G.cn)("relative p-[4px] group",r),children:[(0,i.jsx)(n.P.div,{variants:s?a:void 0,initial:s?"initial":void 0,animate:s?"animate":void 0,transition:s?{duration:5,repeat:1/0,repeatType:"reverse"}:void 0,style:{backgroundSize:s?"400% 400%":void 0},className:(0,G.cn)("absolute inset-0 rounded-3xl z-[1] opacity-60 group-hover:opacity-100 blur-xl transition duration-500 will-change-transform","bg-[radial-gradient(circle_farthest-side_at_0_100%,#0ea5e9,transparent),radial-gradient(circle_farthest-side_at_100%_0,#eab308,transparent),radial-gradient(circle_farthest-side_at_100%_100%,#0284c7,transparent),radial-gradient(circle_farthest-side_at_0_0,#38bdf8,#141316)]")}),(0,i.jsx)(n.P.div,{variants:s?a:void 0,initial:s?"initial":void 0,animate:s?"animate":void 0,transition:s?{duration:5,repeat:1/0,repeatType:"reverse"}:void 0,style:{backgroundSize:s?"400% 400%":void 0},className:(0,G.cn)("absolute inset-0 rounded-3xl z-[1] will-change-transform","bg-[radial-gradient(circle_farthest-side_at_0_100%,#0ea5e9,transparent),radial-gradient(circle_farthest-side_at_100%_0,#eab308,transparent),radial-gradient(circle_farthest-side_at_100%_100%,#0284c7,transparent),radial-gradient(circle_farthest-side_at_0_0,#38bdf8,#141316)]")}),(0,i.jsx)("div",{className:(0,G.cn)("relative z-10",t),children:e})]})};var Y=r(2145),Q=r(4398),J=r(9891),Z=r(6561);let ee=(0,r(2688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);function et(){return(0,i.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden hero-background",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,i.jsx)("div",{className:"absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-blue-400/30 to-blue-600/20 rounded-full blur-3xl animate-float glow-effect"}),(0,i.jsx)("div",{className:"absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-yellow-400/30 to-yellow-600/20 rounded-full blur-3xl animate-float glow-effect-accent",style:{animationDelay:"2s"}}),(0,i.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/5 to-yellow-500/5 rounded-full blur-3xl"}),(0,i.jsx)("div",{className:"container mx-auto px-4 relative z-10",children:(0,i.jsxs)(n.P.div,{variants:G.bK,initial:"initial",animate:"animate",className:"text-center max-w-6xl mx-auto",children:[(0,i.jsxs)(n.P.div,{variants:G.tE,className:"flex items-center justify-center gap-6 mb-8",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 text-sm text-neutral-600",children:[(0,i.jsx)(Q.A,{className:"w-4 h-4 fill-yellow-400 text-yellow-400"}),(0,i.jsx)("span",{children:"5.0 Rating"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2 text-sm text-neutral-600",children:[(0,i.jsx)(J.A,{className:"w-4 h-4 text-blue-600"}),(0,i.jsx)("span",{children:"Fully Insured"})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2 text-sm text-neutral-600",children:[(0,i.jsx)(Z.A,{className:"w-4 h-4 text-yellow-600"}),(0,i.jsx)("span",{children:"Certified Professional"})]})]}),(0,i.jsx)(n.P.div,{variants:G.tE,className:"mb-8",children:(0,i.jsx)(H,{words:"Automotive Artistry That Protects Your Investment",className:"text-4xl md:text-6xl lg:text-8xl font-bold text-center luxury-text-gradient leading-tight"})}),(0,i.jsx)(n.P.p,{variants:G.tE,className:"text-lg md:text-xl lg:text-2xl text-neutral-700 max-w-4xl mx-auto mb-12 leading-relaxed font-medium",children:"Premium detailing services with meticulous attention to every corner of your vehicle. From ceramic coating to paint correction, we deliver results that exceed expectations."}),(0,i.jsxs)(n.P.div,{variants:G.tE,className:"flex flex-col sm:flex-row gap-6 justify-center items-center mb-16",children:[(0,i.jsx)(K,{className:"rounded-full",children:(0,i.jsxs)("button",{className:"bg-white text-neutral-800 px-10 py-5 rounded-full font-bold text-lg flex items-center gap-3 luxury-hover magnetic-hover",children:["Get Free Quote",(0,i.jsx)(ee,{className:"w-5 h-5 transition-transform duration-300 group-hover:translate-x-1"})]})}),(0,i.jsx)(Y.z,{containerClassName:"rounded-full",className:"bg-transparent text-neutral-800 px-10 py-5 font-bold text-lg magnetic-hover",children:"View Our Work"})]}),(0,i.jsxs)(n.P.div,{variants:G.tE,className:"grid md:grid-cols-2 gap-8 max-w-5xl mx-auto",children:[(0,i.jsxs)("div",{className:"relative group",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-red-500/30 to-orange-500/30 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"}),(0,i.jsxs)("div",{className:"relative glass-card rounded-3xl p-8 border border-white/30 luxury-shadow group-hover:luxury-shadow-lg transition-all duration-500",children:[(0,i.jsxs)("div",{className:"aspect-video bg-gradient-to-br from-red-100 to-orange-100 rounded-2xl mb-6 flex items-center justify-center relative overflow-hidden",children:[(0,i.jsx)("div",{className:"absolute top-3 left-3 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold",children:"BEFORE"}),(0,i.jsx)("span",{className:"text-red-600 font-semibold text-lg",children:"Dull & Faded"})]}),(0,i.jsx)("h3",{className:"font-bold text-neutral-800 mb-3 text-xl",children:"Dull & Oxidized"}),(0,i.jsx)("p",{className:"text-neutral-600 leading-relaxed",children:"Faded paint, water spots, and surface contamination diminish your vehicle's appearance"})]})]}),(0,i.jsxs)("div",{className:"relative group",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500/30 to-green-500/30 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"}),(0,i.jsxs)("div",{className:"relative glass-card rounded-3xl p-8 border border-white/30 luxury-shadow group-hover:luxury-shadow-lg transition-all duration-500",children:[(0,i.jsxs)("div",{className:"aspect-video bg-gradient-to-br from-blue-100 to-green-100 rounded-2xl mb-6 flex items-center justify-center relative overflow-hidden",children:[(0,i.jsx)("div",{className:"absolute top-3 right-3 bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold",children:"AFTER"}),(0,i.jsx)("span",{className:"text-green-600 font-semibold text-lg",children:"Showroom Ready"})]}),(0,i.jsx)("h3",{className:"font-bold text-neutral-800 mb-3 text-xl",children:"Showroom Shine"}),(0,i.jsx)("p",{className:"text-neutral-600 leading-relaxed",children:"Deep gloss, protected finish, and mirror-like clarity that exceeds expectations"})]})]})]})]})}),(0,i.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:2,duration:1},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:(0,i.jsxs)("div",{className:"flex flex-col items-center gap-2 text-neutral-400",children:[(0,i.jsx)("span",{className:"text-sm",children:"Scroll to explore"}),(0,i.jsx)(n.P.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},className:"w-6 h-10 border-2 border-neutral-300 rounded-full flex justify-center",children:(0,i.jsx)(n.P.div,{animate:{y:[0,12,0]},transition:{duration:2,repeat:1/0},className:"w-1 h-3 bg-neutral-400 rounded-full mt-2"})})]})})]})}},4862:(e,t,r)=>{"use strict";r.d(t,{Gallery:()=>u});var i=r(687),n=r(3210),s=r(331),a=r(8920),o=r(6360);let l=(0,r(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);function u(){let[e,t]=(0,n.useState)("all"),[r,u]=(0,n.useState)(null),c=[{id:1,category:"ceramic",title:"Tesla Model S - Ceramic Coating",before:"/api/placeholder/400/300",after:"/api/placeholder/400/300",description:"9H ceramic coating application with paint correction"},{id:2,category:"correction",title:"BMW M3 - Paint Correction",before:"/api/placeholder/400/300",after:"/api/placeholder/400/300",description:"Multi-stage paint correction removing years of swirl marks"},{id:3,category:"luxury",title:"Mercedes S-Class - Full Detail",before:"/api/placeholder/400/300",after:"/api/placeholder/400/300",description:"Complete exterior and interior restoration"},{id:4,category:"interior",title:"Porsche 911 - Interior Detail",before:"/api/placeholder/400/300",after:"/api/placeholder/400/300",description:"Leather conditioning and fabric protection"},{id:5,category:"ceramic",title:"Audi RS6 - Ceramic Protection",before:"/api/placeholder/400/300",after:"/api/placeholder/400/300",description:"Premium ceramic coating with 5-year warranty"},{id:6,category:"correction",title:"Lamborghini Hurac\xe1n - Paint Restoration",before:"/api/placeholder/400/300",after:"/api/placeholder/400/300",description:"Exotic car paint correction and protection"}],d="all"===e?c:c.filter(t=>t.category===e);return(0,i.jsxs)("section",{id:"gallery",className:"py-24 bg-gradient-to-b from-white via-neutral-50/30 to-white relative overflow-hidden",children:[(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsxs)(s.P.div,{...o.X1,className:"text-center mb-16",children:[(0,i.jsx)(s.P.span,{...o.X1,className:"inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-4",children:"Our Work"}),(0,i.jsxs)(s.P.h2,{...o.X1,className:"text-4xl md:text-5xl lg:text-7xl font-bold mb-6",children:[(0,i.jsx)("span",{className:"luxury-text-gradient",children:"Before & After"}),(0,i.jsx)("br",{}),(0,i.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-yellow-600 bg-clip-text text-transparent",children:"Gallery"})]}),(0,i.jsx)(s.P.p,{...o.X1,className:"text-lg md:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed",children:"See the dramatic transformations we achieve through our meticulous four-corner approach. Every vehicle tells a story of restoration and protection."})]}),(0,i.jsx)(s.P.div,{...o.X1,className:"flex flex-wrap justify-center gap-4 mb-12",children:[{id:"all",name:"All Work"},{id:"ceramic",name:"Ceramic Coating"},{id:"correction",name:"Paint Correction"},{id:"interior",name:"Interior Detail"},{id:"luxury",name:"Luxury Vehicles"}].map(r=>(0,i.jsx)(s.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>t(r.id),className:`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${e===r.id?"bg-gradient-to-r from-blue-600 to-yellow-600 text-white shadow-lg":"bg-white text-neutral-700 border border-neutral-200 hover:border-blue-300"}`,children:r.name},r.id))}),(0,i.jsx)(s.P.div,{layout:!0,className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:(0,i.jsx)(a.N,{children:d.map((e,t)=>(0,i.jsxs)(s.P.div,{layout:!0,initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.5,delay:.1*t},className:"glass-card rounded-3xl overflow-hidden luxury-shadow border border-white/30 group hover:luxury-shadow-lg transition-all duration-500",children:[(0,i.jsxs)("div",{className:"relative aspect-video overflow-hidden",children:[(0,i.jsxs)("div",{className:"grid grid-cols-2 h-full",children:[(0,i.jsxs)("div",{className:"relative bg-gradient-to-br from-red-100 to-orange-100 flex items-center justify-center",children:[(0,i.jsx)("div",{className:"absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold",children:"BEFORE"}),(0,i.jsx)("div",{className:"text-red-600 font-semibold opacity-50",children:"Before Image"})]}),(0,i.jsxs)("div",{className:"relative bg-gradient-to-br from-blue-100 to-green-100 flex items-center justify-center",children:[(0,i.jsx)("div",{className:"absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold",children:"AFTER"}),(0,i.jsx)("div",{className:"text-green-600 font-semibold opacity-50",children:"After Image"})]})]}),(0,i.jsx)("div",{className:"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center",children:(0,i.jsx)(s.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>u(e.id),className:"bg-white text-neutral-800 p-3 rounded-full shadow-lg",children:(0,i.jsx)(l,{className:"w-6 h-6"})})})]}),(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsx)("h3",{className:"font-bold text-neutral-800 mb-2 text-lg",children:e.title}),(0,i.jsx)("p",{className:"text-neutral-600 text-sm leading-relaxed",children:e.description})]})]},e.id))})}),(0,i.jsx)(s.P.div,{...o.X1,className:"text-center mt-16",children:(0,i.jsx)(s.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-gradient-to-r from-blue-600 to-yellow-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300",children:"See More Work"})})]}),(0,i.jsx)(a.N,{children:r&&(0,i.jsx)(s.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4",onClick:()=>u(null),children:(0,i.jsx)(s.P.div,{initial:{scale:.8},animate:{scale:1},exit:{scale:.8},className:"bg-white rounded-2xl p-4 max-w-4xl w-full",onClick:e=>e.stopPropagation(),children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold mb-4",children:c.find(e=>e.id===r)?.title}),(0,i.jsx)("div",{className:"aspect-video bg-gradient-to-r from-neutral-100 to-neutral-200 rounded-xl flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-neutral-500",children:"Enlarged Before/After View"})})]})})})})]})}},4948:(e,t,r)=>{"use strict";r.d(t,{Y:()=>i,t:()=>n});let i=2e4;function n(e){let t=0,r=e.next(t);for(;!r.done&&t<i;)t+=50,r=e.next(t);return t>=i?1/0:t}},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var i=e[r];if("*"===i||"+"===i||"?"===i){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===i){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===i){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===i){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===i){for(var n="",s=r+1;s<e.length;){var a=e.charCodeAt(s);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){n+=e[s++];continue}break}if(!n)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:n}),r=s;continue}if("("===i){var o=1,l="",s=r+1;if("?"===e[s])throw TypeError('Pattern cannot start with "?" at '+s);for(;s<e.length;){if("\\"===e[s]){l+=e[s++]+e[s++];continue}if(")"===e[s]){if(0==--o){s++;break}}else if("("===e[s]&&(o++,"?"!==e[s+1]))throw TypeError("Capturing groups are not allowed at "+s);l+=e[s++]}if(o)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=s;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),i=t.prefixes,s=void 0===i?"./":i,a="[^"+n(t.delimiter||"/#?")+"]+?",o=[],l=0,u=0,c="",d=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},h=function(e){var t=d(e);if(void 0!==t)return t;var i=r[u];throw TypeError("Unexpected "+i.type+" at "+i.index+", expected "+e)},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};u<r.length;){var m=d("CHAR"),f=d("NAME"),g=d("PATTERN");if(f||g){var x=m||"";-1===s.indexOf(x)&&(c+=x,x=""),c&&(o.push(c),c=""),o.push({name:f||l++,prefix:x,suffix:"",pattern:g||a,modifier:d("MODIFIER")||""});continue}var v=m||d("ESCAPED_CHAR");if(v){c+=v;continue}if(c&&(o.push(c),c=""),d("OPEN")){var x=p(),y=d("NAME")||"",b=d("PATTERN")||"",w=p();h("CLOSE"),o.push({name:y||(b?l++:""),pattern:y&&!b?a:b,prefix:x,suffix:w,modifier:d("MODIFIER")||""});continue}h("END")}return o}function r(e,t){void 0===t&&(t={});var r=s(t),i=t.encode,n=void 0===i?function(e){return e}:i,a=t.validate,o=void 0===a||a,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",i=0;i<e.length;i++){var s=e[i];if("string"==typeof s){r+=s;continue}var a=t?t[s.name]:void 0,u="?"===s.modifier||"*"===s.modifier,c="*"===s.modifier||"+"===s.modifier;if(Array.isArray(a)){if(!c)throw TypeError('Expected "'+s.name+'" to not repeat, but got an array');if(0===a.length){if(u)continue;throw TypeError('Expected "'+s.name+'" to not be empty')}for(var d=0;d<a.length;d++){var h=n(a[d],s);if(o&&!l[i].test(h))throw TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'", but got "'+h+'"');r+=s.prefix+h+s.suffix}continue}if("string"==typeof a||"number"==typeof a){var h=n(String(a),s);if(o&&!l[i].test(h))throw TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but got "'+h+'"');r+=s.prefix+h+s.suffix;continue}if(!u){var p=c?"an array":"a string";throw TypeError('Expected "'+s.name+'" to be '+p)}}return r}}function i(e,t,r){void 0===r&&(r={});var i=r.decode,n=void 0===i?function(e){return e}:i;return function(r){var i=e.exec(r);if(!i)return!1;for(var s=i[0],a=i.index,o=Object.create(null),l=1;l<i.length;l++)!function(e){if(void 0!==i[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?o[r.name]=i[e].split(r.prefix+r.suffix).map(function(e){return n(e,r)}):o[r.name]=n(i[e],r)}}(l);return{path:s,index:a,params:o}}}function n(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function s(e){return e&&e.sensitive?"":"i"}function a(e,t,r){void 0===r&&(r={});for(var i=r.strict,a=void 0!==i&&i,o=r.start,l=r.end,u=r.encode,c=void 0===u?function(e){return e}:u,d="["+n(r.endsWith||"")+"]|$",h="["+n(r.delimiter||"/#?")+"]",p=void 0===o||o?"^":"",m=0;m<e.length;m++){var f=e[m];if("string"==typeof f)p+=n(c(f));else{var g=n(c(f.prefix)),x=n(c(f.suffix));if(f.pattern)if(t&&t.push(f),g||x)if("+"===f.modifier||"*"===f.modifier){var v="*"===f.modifier?"?":"";p+="(?:"+g+"((?:"+f.pattern+")(?:"+x+g+"(?:"+f.pattern+"))*)"+x+")"+v}else p+="(?:"+g+"("+f.pattern+")"+x+")"+f.modifier;else p+="("+f.pattern+")"+f.modifier;else p+="(?:"+g+x+")"+f.modifier}}if(void 0===l||l)a||(p+=h+"?"),p+=r.endsWith?"(?="+d+")":"$";else{var y=e[e.length-1],b="string"==typeof y?h.indexOf(y[y.length-1])>-1:void 0===y;a||(p+="(?:"+h+"(?="+d+"))?"),b||(p+="(?="+h+"|"+d+")")}return new RegExp(p,s(r))}function o(t,r,i){if(t instanceof RegExp){if(!r)return t;var n=t.source.match(/\((?!\?)/g);if(n)for(var l=0;l<n.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,r,i).source}).join("|")+")",s(i)):a(e(t,i),r,i)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,i){return r(e(t,i),i)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return i(o(e,r,t),r,t)},t.regexpToFunction=i,t.tokensToRegexp=a,t.pathToRegexp=o})(),e.exports=t})()},5444:(e,t,r)=>{"use strict";r.d(t,{X4:()=>s,ai:()=>n,hs:()=>a});var i=r(7758);let n={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},s={...n,transform:e=>(0,i.q)(0,1,e)},a={...n,default:1}},5472:(e,t,r)=>{"use strict";r.d(t,{T:()=>a,n:()=>o});var i=r(5444),n=r(2874),s=r(1888);let a=[i.ai,n.px,n.KN,n.uj,n.vw,n.vh,{test:e=>"auto"===e,parse:e=>e}],o=e=>a.find((0,s.w)(e))},5485:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var i=r(9664),n=r(8762);let s=new Set(["brightness","contrast","saturate","opacity"]);function a(e){let[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[i]=r.match(n.S)||[];if(!i)return e;let a=r.replace(i,""),o=+!!s.has(t);return i!==r&&(o*=100),t+"("+o+a+")"}let o=/\b([a-z-]*)\(.*?\)/gu,l={...i.f,getAnimatableNone:e=>{let t=e.match(o);return t?t.map(a).join(" "):e}}},5526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return c},matchHas:function(){return u},parseDestination:function(){return d},prepareDestination:function(){return h}});let i=r(5362),n=r(3293),s=r(6759),a=r(1437),o=r(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,r,i){void 0===r&&(r=[]),void 0===i&&(i=[]);let n={},s=r=>{let i,s=r.key;switch(r.type){case"header":s=s.toLowerCase(),i=e.headers[s];break;case"cookie":i="cookies"in e?e.cookies[r.key]:(0,o.getCookieParser)(e.headers)()[r.key];break;case"query":i=t[s];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};i=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&i)return n[function(e){let t="";for(let r=0;r<e.length;r++){let i=e.charCodeAt(r);(i>64&&i<91||i>96&&i<123)&&(t+=e[r])}return t}(s)]=i,!0;if(i){let e=RegExp("^"+r.value+"$"),t=Array.isArray(i)?i.slice(-1)[0].match(e):i.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{n[e]=t.groups[e]}):"host"===r.type&&t[0]&&(n.host=t[0])),!0}return!1};return!(!r.every(e=>s(e))||i.some(e=>s(e)))&&n}function c(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,i.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,n.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,s.parseUrl)(t),i=r.pathname;i&&(i=l(i));let a=r.href;a&&(a=l(a));let o=r.hostname;o&&(o=l(o));let u=r.hash;return u&&(u=l(u)),{...r,pathname:i,hostname:o,href:a,hash:u}}function h(e){let t,r,n=Object.assign({},e.query),s=d(e),{hostname:o,query:u}=s,h=s.pathname;s.hash&&(h=""+h+s.hash);let p=[],m=[];for(let e of((0,i.pathToRegexp)(h,m),m))p.push(e.name);if(o){let e=[];for(let t of((0,i.pathToRegexp)(o,e),e))p.push(t.name)}let f=(0,i.compile)(h,{validate:!1});for(let[r,n]of(o&&(t=(0,i.compile)(o,{validate:!1})),Object.entries(u)))Array.isArray(n)?u[r]=n.map(t=>c(l(t),e.params)):"string"==typeof n&&(u[r]=c(l(n),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>p.includes(e)))for(let t of g)t in u||(u[t]=e.params[t]);if((0,a.isInterceptionRouteAppPath)(h))for(let t of h.split("/")){let r=a.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[i,n]=(r=f(e.params)).split("#",2);t&&(s.hostname=t(e.params)),s.pathname=i,s.hash=(n?"#":"")+(n||""),delete s.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return s.query={...n,...s.query},{newUrl:r,destQuery:u,parsedDestination:s}}},5531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},5547:(e,t,r)=>{"use strict";function i(e,t){return t?1e3/t*e:0}r.d(t,{f:()=>i})},5723:(e,t,r)=>{"use strict";r.d(t,{f:()=>s});var i=r(4068),n=r(8028);function s(e,t){let r=e[e.length-1];for(let s=1;s<=t;s++){let a=(0,i.q)(0,t,s);e.push((0,n.k)(r,1,a))}}},5726:(e,t,r)=>{"use strict";r.d(t,{U:()=>i,f:()=>n});let i=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],n=new Set(i)},5927:(e,t,r)=>{"use strict";r.d(t,{S:()=>i});let i=e=>!!(e&&e.getVelocity)},5934:(e,t,r)=>{"use strict";r.d(t,{x:()=>s});var i=r(5927),n=r(7609);function s(e,t,r){let{style:s}=e,a={};for(let o in s)((0,i.S)(s[o])||t.style&&(0,i.S)(t.style[o])||(0,n.z)(o,e)||r?.getValue(o)?.liveStyle!==void 0)&&(a[o]=s[o]);return a}},5935:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},5944:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var i=r(5927),n=r(4342),s=r(7238);function a(e,t,r){let a=(0,i.S)(e)?e:(0,n.OQ)(e);return a.start((0,s.f)("",a,t,r)),a.animation}},6017:(e,t,r)=>{"use strict";r.d(t,{Gallery:()=>i});let i=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Gallery() from the server but Gallery is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\sections\\gallery.tsx","Gallery")},6044:(e,t,r)=>{"use strict";r.d(t,{xQ:()=>s});var i=r(3210),n=r(1279);function s(e=!0){let t=(0,i.useContext)(n.t);if(null===t)return[!0,null];let{isPresent:r,onExitComplete:a,register:o}=t,l=(0,i.useId)();(0,i.useEffect)(()=>{if(e)return o(l)},[e]);let u=(0,i.useCallback)(()=>e&&a&&a(l),[l,a,e]);return!r&&a?[!1,u]:[!0]}},6184:(e,t,r)=>{"use strict";r.d(t,{q:()=>i});let i={layout:0,mainThread:0,waapi:0}},6244:(e,t,r)=>{"use strict";r.d(t,{$:()=>i,V:()=>n});let i=()=>{},n=()=>{}},6341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return x},getUtils:function(){return g},interpolateDynamicPath:function(){return m},normalizeDynamicRouteParams:function(){return f},normalizeVercelUrl:function(){return p}});let i=r(9551),n=r(1959),s=r(2437),a=r(4396),o=r(8034),l=r(5526),u=r(2887),c=r(4722),d=r(6143),h=r(7912);function p(e,t,r){let n=(0,i.parse)(e.url,!0);for(let e of(delete n.search,Object.keys(n.query))){let i=e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX),s=e!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(i||s||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete n.query[e]}e.url=(0,i.format)(n)}function m(e,t,r){if(!r)return e;for(let i of Object.keys(r.groups)){let n,{optional:s,repeat:a}=r.groups[i],o=`[${a?"...":""}${i}]`;s&&(o=`[${o}]`);let l=t[i];n=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(o,n)}return e}function f(e,t,r,i){let n={};for(let s of Object.keys(t.groups)){let a=e[s];"string"==typeof a?a=(0,c.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(c.normalizeRscURL));let o=r[s],l=t.groups[s].optional;if((Array.isArray(o)?o.some(e=>Array.isArray(a)?a.some(t=>t.includes(e)):null==a?void 0:a.includes(e)):null==a?void 0:a.includes(o))||void 0===a&&!(l&&i))return{params:{},hasValidParams:!1};l&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${s}]]`))&&(a=void 0,delete e[s]),a&&"string"==typeof a&&t.groups[s].repeat&&(a=a.split("/")),a&&(n[s]=a)}return{params:n,hasValidParams:!0}}function g({page:e,i18n:t,basePath:r,rewrites:i,pageIsDynamic:c,trailingSlash:d,caseSensitive:g}){let x,v,y;return c&&(x=(0,a.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),y=(v=(0,o.getRouteMatcher)(x))(e)),{handleRewrites:function(a,o){let h={},p=o.pathname,m=i=>{let u=(0,s.getPathMatch)(i.source+(d?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!o.pathname)return!1;let m=u(o.pathname);if((i.has||i.missing)&&m){let e=(0,l.matchHas)(a,o.query,i.has,i.missing);e?Object.assign(m,e):m=!1}if(m){let{parsedDestination:s,destQuery:a}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:i.destination,params:m,query:o.query});if(s.protocol)return!0;if(Object.assign(h,a,m),Object.assign(o.query,s.query),delete s.query,Object.assign(o,s),!(p=o.pathname))return!1;if(r&&(p=p.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,n.normalizeLocalePath)(p,t.locales);p=e.pathname,o.query.nextInternalLocale=e.detectedLocale||m.nextInternalLocale}if(p===e)return!0;if(c&&v){let e=v(p);if(e)return o.query={...o.query,...e},!0}}return!1};for(let e of i.beforeFiles||[])m(e);if(p!==e){let t=!1;for(let e of i.afterFiles||[])if(t=m(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(p||"");return t===(0,u.removeTrailingSlash)(e)||(null==v?void 0:v(t))})()){for(let e of i.fallback||[])if(t=m(e))break}}return h},defaultRouteRegex:x,dynamicRouteMatcher:v,defaultRouteMatches:y,getParamsFromRouteMatches:function(e){if(!x)return null;let{groups:t,routeKeys:r}=x,i=(0,o.getRouteMatcher)({re:{exec:e=>{let i=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(i)){let r=(0,h.normalizeNextQueryParam)(e);r&&(i[r]=t,delete i[e])}let n={};for(let e of Object.keys(r)){let s=r[e];if(!s)continue;let a=t[s],o=i[e];if(!a.optional&&!o)return null;n[a.pos]=o}return n}},groups:t})(e);return i||null},normalizeDynamicRouteParams:(e,t)=>x&&y?f(e,x,y,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>p(e,t,x),interpolateDynamicPath:(e,t)=>m(e,t,x)}}function x(e,t){return"string"==typeof e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[d.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6360:(e,t,r)=>{"use strict";r.d(t,{cn:()=>ec,F0:()=>em,tE:()=>ed,X1:()=>ep,bK:()=>eh});let i=e=>{let t=o(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:i}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,t)||a(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&i[e]?[...n,...i[e]]:n}}},n=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],i=t.nextPart.get(r),s=i?n(e.slice(1),i):void 0;if(s)return s;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},s=/^\[(.+)\]$/,a=e=>{if(s.test(e)){let t=s.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},o=e=>{let{theme:t,classGroups:r}=e,i={nextPart:new Map,validators:[]};for(let e in r)l(r[e],i,e,t);return i},l=(e,t,r,i)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e)return c(e)?void l(e(i),t,r,i):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,n])=>{l(n,u(t,e),r,i)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,d=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,i=new Map,n=(n,s)=>{r.set(n,s),++t>e&&(t=0,i=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=i.get(e))?(n(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):n(e,t)}}},h=e=>{let{prefix:t,experimentalParseClassName:r}=e,i=e=>{let t,r=[],i=0,n=0,s=0;for(let a=0;a<e.length;a++){let o=e[a];if(0===i&&0===n){if(":"===o){r.push(e.slice(s,a)),s=a+1;continue}if("/"===o){t=a;continue}}"["===o?i++:"]"===o?i--:"("===o?n++:")"===o&&n--}let a=0===r.length?e:e.substring(s),o=p(a);return{modifiers:r,hasImportantModifier:o!==a,baseClassName:o,maybePostfixModifierPosition:t&&t>s?t-s:void 0}};if(t){let e=t+":",r=i;i=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=i;i=t=>r({className:t,parseClassName:e})}return i},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,m=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],i=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...i.sort(),e),i=[]):i.push(e)}),r.push(...i.sort()),r}},f=e=>({cache:d(e.cacheSize),parseClassName:h(e),sortModifiers:m(e),...i(e)}),g=/\s+/,x=(e,t)=>{let{parseClassName:r,getClassGroupId:i,getConflictingClassGroupIds:n,sortModifiers:s}=t,a=[],o=e.trim().split(g),l="";for(let e=o.length-1;e>=0;e-=1){let t=o[e],{isExternal:u,modifiers:c,hasImportantModifier:d,baseClassName:h,maybePostfixModifierPosition:p}=r(t);if(u){l=t+(l.length>0?" "+l:l);continue}let m=!!p,f=i(m?h.substring(0,p):h);if(!f){if(!m||!(f=i(h))){l=t+(l.length>0?" "+l:l);continue}m=!1}let g=s(c).join(":"),x=d?g+"!":g,v=x+f;if(a.includes(v))continue;a.push(v);let y=n(f,m);for(let e=0;e<y.length;++e){let t=y[e];a.push(x+t)}l=t+(l.length>0?" "+l:l)}return l};function v(){let e,t,r=0,i="";for(;r<arguments.length;)(e=arguments[r++])&&(t=y(e))&&(i&&(i+=" "),i+=t);return i}let y=e=>{let t;if("string"==typeof e)return e;let r="";for(let i=0;i<e.length;i++)e[i]&&(t=y(e[i]))&&(r&&(r+=" "),r+=t);return r},b=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},w=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,j=/^\((?:(\w[\w-]*):)?(.+)\)$/i,P=/^\d+\/\d+$/,k=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,A=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,T=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,E=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,N=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,S=e=>P.test(e),C=e=>!!e&&!Number.isNaN(Number(e)),M=e=>!!e&&Number.isInteger(Number(e)),R=e=>e.endsWith("%")&&C(e.slice(0,-1)),D=e=>k.test(e),V=()=>!0,O=e=>A.test(e)&&!T.test(e),_=()=>!1,F=e=>E.test(e),L=e=>N.test(e),I=e=>!$(e)&&!G(e),z=e=>ee(e,en,_),$=e=>w.test(e),B=e=>ee(e,es,O),U=e=>ee(e,ea,C),W=e=>ee(e,er,_),q=e=>ee(e,ei,L),X=e=>ee(e,el,F),G=e=>j.test(e),H=e=>et(e,es),K=e=>et(e,eo),Y=e=>et(e,er),Q=e=>et(e,en),J=e=>et(e,ei),Z=e=>et(e,el,!0),ee=(e,t,r)=>{let i=w.exec(e);return!!i&&(i[1]?t(i[1]):r(i[2]))},et=(e,t,r=!1)=>{let i=j.exec(e);return!!i&&(i[1]?t(i[1]):r)},er=e=>"position"===e||"percentage"===e,ei=e=>"image"===e||"url"===e,en=e=>"length"===e||"size"===e||"bg-size"===e,es=e=>"length"===e,ea=e=>"number"===e,eo=e=>"family-name"===e,el=e=>"shadow"===e;Symbol.toStringTag;let eu=function(e,...t){let r,i,n,s=function(o){return i=(r=f(t.reduce((e,t)=>t(e),e()))).cache.get,n=r.cache.set,s=a,a(o)};function a(e){let t=i(e);if(t)return t;let s=x(e,r);return n(e,s),s}return function(){return s(v.apply(null,arguments))}}(()=>{let e=b("color"),t=b("font"),r=b("text"),i=b("font-weight"),n=b("tracking"),s=b("leading"),a=b("breakpoint"),o=b("container"),l=b("spacing"),u=b("radius"),c=b("shadow"),d=b("inset-shadow"),h=b("text-shadow"),p=b("drop-shadow"),m=b("blur"),f=b("perspective"),g=b("aspect"),x=b("ease"),v=b("animate"),y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],w=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],j=()=>[...w(),G,$],P=()=>["auto","hidden","clip","visible","scroll"],k=()=>["auto","contain","none"],A=()=>[G,$,l],T=()=>[S,"full","auto",...A()],E=()=>[M,"none","subgrid",G,$],N=()=>["auto",{span:["full",M,G,$]},M,G,$],O=()=>[M,"auto",G,$],_=()=>["auto","min","max","fr",G,$],F=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],L=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...A()],et=()=>[S,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...A()],er=()=>[e,G,$],ei=()=>[...w(),Y,W,{position:[G,$]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],es=()=>["auto","cover","contain",Q,z,{size:[G,$]}],ea=()=>[R,H,B],eo=()=>["","none","full",u,G,$],el=()=>["",C,H,B],eu=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[C,R,Y,W],eh=()=>["","none",m,G,$],ep=()=>["none",C,G,$],em=()=>["none",C,G,$],ef=()=>[C,G,$],eg=()=>[S,"full",...A()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[D],breakpoint:[D],color:[V],container:[D],"drop-shadow":[D],ease:["in","out","in-out"],font:[I],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[D],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[D],shadow:[D],spacing:["px",C],text:[D],"text-shadow":[D],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",S,$,G,g]}],container:["container"],columns:[{columns:[C,$,G,o]}],"break-after":[{"break-after":y()}],"break-before":[{"break-before":y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:j()}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:k()}],"overscroll-x":[{"overscroll-x":k()}],"overscroll-y":[{"overscroll-y":k()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:T()}],"inset-x":[{"inset-x":T()}],"inset-y":[{"inset-y":T()}],start:[{start:T()}],end:[{end:T()}],top:[{top:T()}],right:[{right:T()}],bottom:[{bottom:T()}],left:[{left:T()}],visibility:["visible","invisible","collapse"],z:[{z:[M,"auto",G,$]}],basis:[{basis:[S,"full","auto",o,...A()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[C,S,"auto","initial","none",$]}],grow:[{grow:["",C,G,$]}],shrink:[{shrink:["",C,G,$]}],order:[{order:[M,"first","last","none",G,$]}],"grid-cols":[{"grid-cols":E()}],"col-start-end":[{col:N()}],"col-start":[{"col-start":O()}],"col-end":[{"col-end":O()}],"grid-rows":[{"grid-rows":E()}],"row-start-end":[{row:N()}],"row-start":[{"row-start":O()}],"row-end":[{"row-end":O()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":_()}],"auto-rows":[{"auto-rows":_()}],gap:[{gap:A()}],"gap-x":[{"gap-x":A()}],"gap-y":[{"gap-y":A()}],"justify-content":[{justify:[...F(),"normal"]}],"justify-items":[{"justify-items":[...L(),"normal"]}],"justify-self":[{"justify-self":["auto",...L()]}],"align-content":[{content:["normal",...F()]}],"align-items":[{items:[...L(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...L(),{baseline:["","last"]}]}],"place-content":[{"place-content":F()}],"place-items":[{"place-items":[...L(),"baseline"]}],"place-self":[{"place-self":["auto",...L()]}],p:[{p:A()}],px:[{px:A()}],py:[{py:A()}],ps:[{ps:A()}],pe:[{pe:A()}],pt:[{pt:A()}],pr:[{pr:A()}],pb:[{pb:A()}],pl:[{pl:A()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":A()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":A()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[o,"screen",...et()]}],"min-w":[{"min-w":[o,"screen","none",...et()]}],"max-w":[{"max-w":[o,"screen","none","prose",{screen:[a]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,H,B]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[i,G,U]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",R,$]}],"font-family":[{font:[K,$,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,G,$]}],"line-clamp":[{"line-clamp":[C,"none",G,U]}],leading:[{leading:[s,...A()]}],"list-image":[{"list-image":["none",G,$]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",G,$]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...eu(),"wavy"]}],"text-decoration-thickness":[{decoration:[C,"from-font","auto",G,B]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[C,"auto",G,$]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:A()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",G,$]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",G,$]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ei()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:es()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},M,G,$],radial:["",G,$],conic:[M,G,$]},J,q]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:ea()}],"gradient-via-pos":[{via:ea()}],"gradient-to-pos":[{to:ea()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:eo()}],"rounded-s":[{"rounded-s":eo()}],"rounded-e":[{"rounded-e":eo()}],"rounded-t":[{"rounded-t":eo()}],"rounded-r":[{"rounded-r":eo()}],"rounded-b":[{"rounded-b":eo()}],"rounded-l":[{"rounded-l":eo()}],"rounded-ss":[{"rounded-ss":eo()}],"rounded-se":[{"rounded-se":eo()}],"rounded-ee":[{"rounded-ee":eo()}],"rounded-es":[{"rounded-es":eo()}],"rounded-tl":[{"rounded-tl":eo()}],"rounded-tr":[{"rounded-tr":eo()}],"rounded-br":[{"rounded-br":eo()}],"rounded-bl":[{"rounded-bl":eo()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...eu(),"hidden","none"]}],"divide-style":[{divide:[...eu(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...eu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[C,G,$]}],"outline-w":[{outline:["",C,H,B]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",c,Z,X]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",d,Z,X]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[C,B]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",h,Z,X]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[C,G,$]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[C]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[G,$]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":w()}],"mask-image-conic-pos":[{"mask-conic":[C]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ei()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:es()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",G,$]}],filter:[{filter:["","none",G,$]}],blur:[{blur:eh()}],brightness:[{brightness:[C,G,$]}],contrast:[{contrast:[C,G,$]}],"drop-shadow":[{"drop-shadow":["","none",p,Z,X]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",C,G,$]}],"hue-rotate":[{"hue-rotate":[C,G,$]}],invert:[{invert:["",C,G,$]}],saturate:[{saturate:[C,G,$]}],sepia:[{sepia:["",C,G,$]}],"backdrop-filter":[{"backdrop-filter":["","none",G,$]}],"backdrop-blur":[{"backdrop-blur":eh()}],"backdrop-brightness":[{"backdrop-brightness":[C,G,$]}],"backdrop-contrast":[{"backdrop-contrast":[C,G,$]}],"backdrop-grayscale":[{"backdrop-grayscale":["",C,G,$]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[C,G,$]}],"backdrop-invert":[{"backdrop-invert":["",C,G,$]}],"backdrop-opacity":[{"backdrop-opacity":[C,G,$]}],"backdrop-saturate":[{"backdrop-saturate":[C,G,$]}],"backdrop-sepia":[{"backdrop-sepia":["",C,G,$]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":A()}],"border-spacing-x":[{"border-spacing-x":A()}],"border-spacing-y":[{"border-spacing-y":A()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",G,$]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[C,"initial",G,$]}],ease:[{ease:["linear","initial",x,G,$]}],delay:[{delay:[C,G,$]}],animate:[{animate:["none",v,G,$]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,G,$]}],"perspective-origin":[{"perspective-origin":j()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:ef()}],"skew-x":[{"skew-x":ef()}],"skew-y":[{"skew-y":ef()}],transform:[{transform:[G,$,"","none","gpu","cpu"]}],"transform-origin":[{origin:j()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",G,$]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":A()}],"scroll-mx":[{"scroll-mx":A()}],"scroll-my":[{"scroll-my":A()}],"scroll-ms":[{"scroll-ms":A()}],"scroll-me":[{"scroll-me":A()}],"scroll-mt":[{"scroll-mt":A()}],"scroll-mr":[{"scroll-mr":A()}],"scroll-mb":[{"scroll-mb":A()}],"scroll-ml":[{"scroll-ml":A()}],"scroll-p":[{"scroll-p":A()}],"scroll-px":[{"scroll-px":A()}],"scroll-py":[{"scroll-py":A()}],"scroll-ps":[{"scroll-ps":A()}],"scroll-pe":[{"scroll-pe":A()}],"scroll-pt":[{"scroll-pt":A()}],"scroll-pr":[{"scroll-pr":A()}],"scroll-pb":[{"scroll-pb":A()}],"scroll-pl":[{"scroll-pl":A()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",G,$]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[C,H,B,U]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function ec(...e){return eu(function(){for(var e,t,r=0,i="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,i,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var s=t.length;for(r=0;r<s;r++)t[r]&&(i=e(t[r]))&&(n&&(n+=" "),n+=i)}else for(i in t)t[i]&&(n&&(n+=" "),n+=i);return n}(e))&&(i&&(i+=" "),i+=t);return i}(e))}let ed={initial:{opacity:0,y:60},animate:{opacity:1,y:0},transition:{duration:.6,ease:[.6,-.05,.01,.99]}},eh={initial:{},animate:{transition:{staggerChildren:.1,delayChildren:.3}}},ep={initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.8,ease:[.6,-.05,.01,.99]},viewport:{once:!0,margin:"-100px"}},em=(e=0)=>({initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.8,ease:[.6,-.05,.01,.99],delay:e},viewport:{once:!0,margin:"-100px"}})},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var n={},s=t.split(i),a=(r||{}).decode||e,o=0;o<s.length;o++){var l=s[o],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==n[c]&&(n[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,a))}}return n},t.serialize=function(e,t,i){var s=i||{},a=s.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!n.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!n.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=s.maxAge){var u=s.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(s.domain){if(!n.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!n.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,i=/; */,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6561:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(2688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},6570:(e,t,r)=>{"use strict";function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}r.d(t,{N:()=>i})},6633:(e,t,r)=>{"use strict";r.d(t,{$:()=>s,H:()=>n});var i=r(2238);let n={};function s(e){for(let t in e)n[t]=e[t],(0,i.j)(t)&&(n[t].isCSSVariable=!0)}},6759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return s}});let i=r(2785),n=r(3736);function s(e){if(e.startsWith("/"))return(0,n.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,i.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},6954:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});let i=e=>/^0[^.\s]+$/u.test(e)},7044:(e,t,r)=>{"use strict";r.d(t,{B:()=>i});let i="undefined"!=typeof window},7095:(e,t,r)=>{"use strict";r.d(t,{a:()=>i});let i=e=>Math.round(1e5*e)/1e5},7146:(e,t,r)=>{"use strict";r.d(t,{D:()=>a});var i=r(7504),n=r(5485);let s={...r(748).W,color:i.y,backgroundColor:i.y,outlineColor:i.y,fill:i.y,stroke:i.y,borderColor:i.y,borderTopColor:i.y,borderRightColor:i.y,borderBottomColor:i.y,borderLeftColor:i.y,filter:n.p,WebkitFilter:n.p},a=e=>s[e]},7211:(e,t,r)=>{"use strict";r.d(t,{X:()=>n,f:()=>i});let i=e=>1e3*e,n=e=>e/1e3},7236:(e,t,r)=>{"use strict";r.d(t,{$:()=>s,q:()=>a});var i=r(8762);let n=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,s=(e,t)=>r=>!!("string"==typeof r&&n.test(r)&&r.startsWith(e)||t&&null!=r&&Object.prototype.hasOwnProperty.call(r,t)),a=(e,t,r)=>n=>{if("string"!=typeof n)return n;let[s,a,o,l]=n.match(i.S);return{[e]:parseFloat(s),[t]:parseFloat(a),[r]:parseFloat(o),alpha:void 0!==l?parseFloat(l):1}}},7238:(e,t,r)=>{"use strict";r.d(t,{f:()=>eN});var i=r(2923),n=r(3671),s=r(8205),a=r(7758),o=r(7211),l=r(4325),u=r(6184),c=r(6244),d=r(2238),h=r(7504),p=r(9664),m=r(3063),f=r(2742);function g(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}var x=r(1874);function v(e,t){return r=>r>0?t:e}var y=r(8028);let b=(e,t,r)=>{let i=e*e,n=r*(t*t-i)+i;return n<0?0:Math.sqrt(n)},w=[m.u,x.B,f.V],j=e=>w.find(t=>t.test(e));function P(e){let t=j(e);if((0,c.$)(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let r=t.parse(e);return t===f.V&&(r=function({hue:e,saturation:t,lightness:r,alpha:i}){e/=360,r/=100;let n=0,s=0,a=0;if(t/=100){let i=r<.5?r*(1+t):r+t-r*t,o=2*r-i;n=g(o,i,e+1/3),s=g(o,i,e),a=g(o,i,e-1/3)}else n=s=a=r;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*a),alpha:i}}(r)),r}let k=(e,t)=>{let r=P(e),i=P(t);if(!r||!i)return v(e,t);let n={...r};return e=>(n.red=b(r.red,i.red,e),n.green=b(r.green,i.green,e),n.blue=b(r.blue,i.blue,e),n.alpha=(0,y.k)(r.alpha,i.alpha,e),x.B.transform(n))},A=new Set(["none","hidden"]);function T(e,t){return r=>(0,y.k)(e,t,r)}function E(e){return"number"==typeof e?T:"string"==typeof e?(0,d.p)(e)?v:h.y.test(e)?k:C:Array.isArray(e)?N:"object"==typeof e?h.y.test(e)?k:S:v}function N(e,t){let r=[...e],i=r.length,n=e.map((e,r)=>E(e)(e,t[r]));return e=>{for(let t=0;t<i;t++)r[t]=n[t](e);return r}}function S(e,t){let r={...e,...t},i={};for(let n in r)void 0!==e[n]&&void 0!==t[n]&&(i[n]=E(e[n])(e[n],t[n]));return e=>{for(let t in i)r[t]=i[t](e);return r}}let C=(e,t)=>{let r=p.f.createTransformer(t),i=(0,p.V)(e),n=(0,p.V)(t);return i.indexes.var.length===n.indexes.var.length&&i.indexes.color.length===n.indexes.color.length&&i.indexes.number.length>=n.indexes.number.length?A.has(e)&&!n.values.length||A.has(t)&&!i.values.length?function(e,t){return A.has(e)?r=>r<=0?e:t:r=>r>=1?t:e}(e,t):(0,s.F)(N(function(e,t){let r=[],i={color:0,var:0,number:0};for(let n=0;n<t.values.length;n++){let s=t.types[n],a=e.indexes[s][i[s]],o=e.values[a]??0;r[n]=o,i[s]++}return r}(i,n),n.values),r):((0,c.$)(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),v(e,t))};function M(e,t,r){return"number"==typeof e&&"number"==typeof t&&"number"==typeof r?(0,y.k)(e,t,r):E(e)(e,t)}let R=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>n.Gt.update(t,e),stop:()=>(0,n.WG)(t),now:()=>n.uv.isProcessing?n.uv.timestamp:l.k.now()}};var D=r(2769),V=r(1062);function O({keyframes:e,velocity:t=0,power:r=.8,timeConstant:i=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:c}){let d,h,p=e[0],m={done:!1,value:p},f=e=>void 0!==o&&e<o||void 0!==l&&e>l,g=e=>void 0===o?l:void 0===l||Math.abs(o-e)<Math.abs(l-e)?o:l,x=r*t,v=p+x,y=void 0===a?v:a(v);y!==v&&(x=y-p);let b=e=>-x*Math.exp(-e/i),w=e=>y+b(e),j=e=>{let t=b(e),r=w(e);m.done=Math.abs(t)<=u,m.value=m.done?y:r},P=e=>{f(m.value)&&(d=e,h=(0,D.o)({keyframes:[m.value,g(m.value)],velocity:(0,V.Y)(w,e,m.value),damping:n,stiffness:s,restDelta:u,restSpeed:c}))};return P(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,j(e),P(e)),void 0!==d&&e>=d)?h.next(e-d):(t||j(e),m)}}}var _=r(4799),F=r(9527),L=r(4819),I=r(7819),z=r(3361),$=r(4068),B=r(8267);function U({duration:e=300,keyframes:t,times:r,ease:i="easeInOut"}){var n;let o=(0,F.h)(i)?i.map(L.K):(0,L.K)(i),l={done:!1,value:t[0]},u=function(e,t,{clamp:r=!0,ease:i,mixer:n}={}){let o=e.length;if((0,c.V)(o===t.length,"Both input and output ranges must be the same length"),1===o)return()=>t[0];if(2===o&&t[0]===t[1])return()=>t[1];let l=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let u=function(e,t,r){let i=[],n=r||I.W.mix||M,a=e.length-1;for(let r=0;r<a;r++){let a=n(e[r],e[r+1]);if(t){let e=Array.isArray(t)?t[r]||z.l:t;a=(0,s.F)(e,a)}i.push(a)}return i}(t,i,n),d=u.length,h=r=>{if(l&&r<e[0])return t[0];let i=0;if(d>1)for(;i<e.length-2&&!(r<e[i+1]);i++);let n=(0,$.q)(e[i],e[i+1],r);return u[i](n)};return r?t=>h((0,a.q)(e[0],e[o-1],t)):h}((n=r&&r.length===t.length?r:(0,B.Z)(t),n.map(t=>t*e)),t,{ease:Array.isArray(o)?o:t.map(()=>o||_.am).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(l.value=u(t),l.done=t>=e,l)}}var W=r(4948);let q=e=>null!==e;function X(e,{repeat:t,repeatType:r="loop"},i,n=1){let s=e.filter(q),a=n<0||t&&"loop"!==r&&t%2==1?0:s.length-1;return a&&void 0!==i?i:s[a]}let G={decay:O,inertia:O,tween:U,keyframes:U,spring:D.o};function H(e){"string"==typeof e.type&&(e.type=G[e.type])}class K{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let Y=e=>e/100;class Q extends K{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==l.k.now()&&this.tick(l.k.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},u.q.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;H(e);let{type:t=U,repeat:r=0,repeatDelay:i=0,repeatType:n,velocity:a=0}=e,{keyframes:o}=e,l=t||U;l!==U&&"number"!=typeof o[0]&&(this.mixKeyframes=(0,s.F)(Y,M(o[0],o[1])),o=[0,100]);let u=l({...e,keyframes:o});"mirror"===n&&(this.mirroredGenerator=l({...e,keyframes:[...o].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=(0,W.t)(u));let{calculatedDuration:c}=u;this.calculatedDuration=c,this.resolvedDuration=c+i,this.totalDuration=this.resolvedDuration*(r+1)-i,this.generator=u}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:r,totalDuration:i,mixKeyframes:n,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:l}=this;if(null===this.startTime)return r.next(0);let{delay:u=0,keyframes:c,repeat:d,repeatType:h,repeatDelay:p,type:m,onUpdate:f,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-i/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let x=this.currentTime-u*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?x<0:x>i;this.currentTime=Math.max(x,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let y=this.currentTime,b=r;if(d){let e=Math.min(this.currentTime,i)/o,t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,(t=Math.min(t,d+1))%2&&("reverse"===h?(r=1-r,p&&(r-=p/o)):"mirror"===h&&(b=s)),y=(0,a.q)(0,1,r)*o}let w=v?{done:!1,value:c[0]}:b.next(y);n&&(w.value=n(w.value));let{done:j}=w;v||null===l||(j=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&j);return P&&m!==O&&(w.value=X(c,this.options,g,this.speed)),f&&f(w.value),P&&this.finish(),w}then(e,t){return this.finished.then(e,t)}get duration(){return(0,o.X)(this.calculatedDuration)}get time(){return(0,o.X)(this.currentTime)}set time(e){e=(0,o.f)(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(l.k.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=(0,o.X)(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=R,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let r=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=r):null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime||(this.startTime=t??r),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(l.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,u.q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}var J=r(736);let Z=e=>e.startsWith("--");function ee(e){let t;return()=>(void 0===t&&(t=e()),t)}let et=ee(()=>void 0!==window.ScrollTimeline);var er=r(2082),ei=r(4177);let en={},es=function(e,t){let r=ee(e);return()=>en[t]??r()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing");var ea=r(8347);let eo=([e,t,r,i])=>`cubic-bezier(${e}, ${t}, ${r}, ${i})`,el={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eo([0,.65,.55,1]),circOut:eo([.55,0,1,.45]),backIn:eo([.31,.01,.66,-.59]),backOut:eo([.33,1.53,.69,.99])};var eu=r(1008);class ec extends K{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:r,keyframes:i,pseudoElement:n,allowFlatten:s=!1,finalKeyframe:a,onComplete:o}=e;this.isPseudoElement=!!n,this.allowFlatten=s,this.options=e,(0,c.V)("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return(0,eu.W)(e)&&es()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,r,{delay:i=0,duration:n=300,repeat:s=0,repeatType:a="loop",ease:o="easeOut",times:l}={},c){let d={[t]:r};l&&(d.offset=l);let h=function e(t,r){if(t)return"function"==typeof t?es()?(0,ea.K)(t,r):"ease-out":(0,ei.D)(t)?eo(t):Array.isArray(t)?t.map(t=>e(t,r)||el.easeOut):el[t]}(o,n);Array.isArray(h)&&(d.easing=h),er.Q.value&&u.q.waapi++;let p={delay:i,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:s+1,direction:"reverse"===a?"alternate":"normal"};c&&(p.pseudoElement=c);let m=e.animate(d,p);return er.Q.value&&m.finished.finally(()=>{u.q.waapi--}),m}(t,r,i,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let e=X(i,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,r){Z(t)?e.style.setProperty(t,r):e.style[t]=r}(t,r,e),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let e=this.animation.effect?.getComputedTiming?.().duration||0;return(0,o.X)(Number(e))}get time(){return(0,o.X)(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=(0,o.f)(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&et())?(this.animation.timeline=e,z.l):t(this)}}var ed=r(3685),eh=r(912),ep=r(2716);let em={anticipate:ed.b,backInOut:eh.ZZ,circInOut:ep.tn};class ef extends ec{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in em&&(e.ease=em[e.ease])}(e),H(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:r,onComplete:i,element:n,...s}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let a=new Q({...s,autoplay:!1}),l=(0,o.f)(this.finishedTime??this.time);t.setWithVelocity(a.sample(l-10).value,a.sample(l).value,10),a.stop()}}let eg=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(p.f.test(e)||"0"===e)&&!e.startsWith("url("));var ex=r(8171);let ev=new Set(["opacity","clipPath","filter","transform"]),ey=ee(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eb extends K{constructor({autoplay:e=!0,delay:t=0,type:r="keyframes",repeat:i=0,repeatDelay:n=0,repeatType:s="loop",keyframes:a,name:o,motionValue:u,element:c,...d}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=l.k.now();let h={autoplay:e,delay:t,type:r,repeat:i,repeatDelay:n,repeatType:s,name:o,motionValue:u,element:c,...d},p=c?.KeyframeResolver||J.h;this.keyframeResolver=new p(a,(e,t,r)=>this.onKeyframesResolved(e,t,h,!r),o,u,c),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,r,i){this.keyframeResolver=void 0;let{name:n,type:s,velocity:a,delay:o,isHandoff:u,onUpdate:d}=r;this.resolvedAt=l.k.now(),!function(e,t,r,i){let n=e[0];if(null===n)return!1;if("display"===t||"visibility"===t)return!0;let s=e[e.length-1],a=eg(n,t),o=eg(s,t);return(0,c.$)(a===o,`You are trying to animate ${t} from "${n}" to "${s}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${s} via the \`style\` property.`),!!a&&!!o&&(function(e){let t=e[0];if(1===e.length)return!0;for(let r=0;r<e.length;r++)if(e[r]!==t)return!0}(e)||("spring"===r||(0,eu.W)(r))&&i)}(e,n,s,a)&&((I.W.instantAnimations||!o)&&d?.(X(e,r,t)),e[0]=e[e.length-1],r.duration=0,r.repeat=0);let h={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...r,keyframes:e},p=!u&&function(e){let{motionValue:t,name:r,repeatDelay:i,repeatType:n,damping:s,type:a}=e;if(!(0,ex.s)(t?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=t.owner.getProps();return ey()&&r&&ev.has(r)&&("transform"!==r||!l)&&!o&&!i&&"mirror"!==n&&0!==s&&"inertia"!==a}(h)?new ef({...h,element:h.motionValue.owner.current}):new Q(h);p.finished.then(()=>this.notifyFinished()).catch(z.l),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),(0,J.q)()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let ew=e=>null!==e;var ej=r(5726);let eP={type:"spring",stiffness:500,damping:25,restSpeed:10},ek=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),eA={type:"keyframes",duration:.8},eT={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},eE=(e,{keyframes:t})=>t.length>2?eA:ej.f.has(e)?e.startsWith("scale")?ek(t[1]):eP:eT,eN=(e,t,r,s={},a,l)=>u=>{let c=(0,i.r)(s,e)||{},d=c.delay||s.delay||0,{elapsed:h=0}=s;h-=(0,o.f)(d);let p={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:t.getVelocity(),...c,delay:-h,onUpdate:e=>{t.set(e),c.onUpdate&&c.onUpdate(e)},onComplete:()=>{u(),c.onComplete&&c.onComplete()},name:e,motionValue:t,element:l?void 0:a};!function({when:e,delay:t,delayChildren:r,staggerChildren:i,staggerDirection:n,repeat:s,repeatType:a,repeatDelay:o,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(c)&&Object.assign(p,eE(e,p)),p.duration&&(p.duration=(0,o.f)(p.duration)),p.repeatDelay&&(p.repeatDelay=(0,o.f)(p.repeatDelay)),void 0!==p.from&&(p.keyframes[0]=p.from);let m=!1;if(!1!==p.type&&(0!==p.duration||p.repeatDelay)||(p.duration=0,0===p.delay&&(m=!0)),(I.W.instantAnimations||I.W.skipAnimations)&&(m=!0,p.duration=0,p.delay=0),p.allowFlatten=!c.type&&!c.ease,m&&!l&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:r="loop"},i){let n=e.filter(ew),s=t&&"loop"!==r&&t%2==1?0:n.length-1;return n[s]}(p.keyframes,c);if(void 0!==e)return void n.Gt.update(()=>{p.onUpdate(e),p.onComplete()})}return c.isSync?new Q(p):new eb(p)}},7283:(e,t,r)=>{"use strict";r.d(t,{g:()=>s});var i=r(7819),n=r(5927);function s(e,t){let r=e.getValue("willChange");if((0,n.S)(r)&&r.add)return r.add(t);if(!r&&i.W.WillChange){let r=new i.W.WillChange("auto");e.addValue("willChange",r),r.add(t)}}},7292:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});let i=e=>Array.isArray(e)},7483:(e,t,r)=>{"use strict";r.d(t,{Services:()=>y});var i=r(687),n=r(331),s=r(6360),a=r(8920),o=r(3210);let l=({items:e,className:t})=>{let[r,l]=(0,o.useState)(null);return(0,i.jsx)("div",{className:(0,s.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 py-10",t),children:e.map((e,t)=>(0,i.jsxs)("div",{className:"relative group block p-2 h-full w-full",onMouseEnter:()=>l(t),onMouseLeave:()=>l(null),children:[(0,i.jsx)(a.N,{children:r===t&&(0,i.jsx)(n.P.span,{className:"absolute inset-0 h-full w-full bg-neutral-200 dark:bg-slate-800/[0.8] block rounded-3xl",layoutId:"hoverBackground",initial:{opacity:0},animate:{opacity:1,transition:{duration:.15}},exit:{opacity:0,transition:{duration:.15,delay:.2}}})}),(0,i.jsxs)(u,{children:[e.icon&&(0,i.jsx)("div",{className:"flex items-center justify-center w-12 h-12 mb-4 rounded-lg bg-gradient-to-br from-blue-500 to-yellow-500",children:e.icon}),(0,i.jsx)(c,{children:e.title}),(0,i.jsx)(d,{children:e.description})]})]},e?.link||t))})},u=({className:e,children:t})=>(0,i.jsx)("div",{className:(0,s.cn)("rounded-3xl h-full w-full p-6 overflow-hidden glass-card border border-white/30 group-hover:border-white/50 relative z-20 card-shadow group-hover:luxury-shadow-lg transition-all duration-500",e),children:(0,i.jsx)("div",{className:"relative z-50",children:(0,i.jsx)("div",{className:"p-2",children:t})})}),c=({className:e,children:t})=>(0,i.jsx)("h4",{className:(0,s.cn)("text-neutral-800 dark:text-neutral-100 font-bold tracking-wide mt-6 text-xl",e),children:t}),d=({className:e,children:t})=>(0,i.jsx)("p",{className:(0,s.cn)("mt-4 text-neutral-600 dark:text-neutral-400 tracking-wide leading-relaxed text-base",e),children:t});var h=r(9891),p=r(2688);let m=(0,p.A)("paintbrush",[["path",{d:"m14.622 17.897-10.68-2.913",key:"vj2p1u"}],["path",{d:"M18.376 2.622a1 1 0 1 1 3.002 3.002L17.36 9.643a.5.5 0 0 0 0 .707l.944.944a2.41 2.41 0 0 1 0 3.408l-.944.944a.5.5 0 0 1-.707 0L8.354 7.348a.5.5 0 0 1 0-.707l.944-.944a2.41 2.41 0 0 1 3.408 0l.944.944a.5.5 0 0 0 .707 0z",key:"18tc5c"}],["path",{d:"M9 8c-1.804 2.71-3.97 3.46-6.583 3.948a.507.507 0 0 0-.302.819l7.32 8.883a1 1 0 0 0 1.185.204C12.735 20.405 16 16.792 16 15",key:"ytzfxy"}]]),f=(0,p.A)("truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]]),g=(0,p.A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),x=(0,p.A)("droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]]),v=(0,p.A)("car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]]);function y(){let e=[{title:"Ceramic Coating",description:"Advanced nano-ceramic protection that bonds to your paint, providing years of protection against UV rays, chemicals, and environmental contaminants.",icon:(0,i.jsx)(h.A,{className:"w-6 h-6 text-white"})},{title:"Paint Correction",description:"Multi-stage polishing process to remove swirl marks, scratches, and oxidation, restoring your vehicle's paint to better-than-new condition.",icon:(0,i.jsx)(m,{className:"w-6 h-6 text-white"})},{title:"Mobile Detailing",description:"Professional detailing services brought directly to your location. Convenience without compromising on quality or attention to detail.",icon:(0,i.jsx)(f,{className:"w-6 h-6 text-white"})},{title:"Interior Deep Clean",description:"Comprehensive interior restoration including leather conditioning, fabric protection, and steam cleaning for a fresh, pristine cabin.",icon:(0,i.jsx)(g,{className:"w-6 h-6 text-white"})},{title:"Hydrophobic Treatment",description:"Advanced water-repelling technology that makes maintenance easier while providing superior protection against water damage and staining.",icon:(0,i.jsx)(x,{className:"w-6 h-6 text-white"})},{title:"Full Vehicle Restoration",description:"Complete transformation service combining all our expertise to bring neglected vehicles back to showroom condition and beyond.",icon:(0,i.jsx)(v,{className:"w-6 h-6 text-white"})}];return(0,i.jsxs)("section",{className:"py-24 bg-gradient-to-b from-white via-neutral-50/50 to-white relative overflow-hidden",children:[(0,i.jsx)("div",{className:"absolute top-0 left-0 w-full h-2 luxury-gradient"}),(0,i.jsx)("div",{className:"absolute top-10 right-10 w-64 h-64 bg-gradient-to-r from-blue-400/10 to-yellow-400/10 rounded-full blur-3xl"}),(0,i.jsx)("div",{className:"absolute bottom-10 left-10 w-80 h-80 bg-gradient-to-r from-yellow-400/10 to-blue-400/10 rounded-full blur-3xl"}),(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsxs)(n.P.div,{...s.X1,className:"text-center mb-16",children:[(0,i.jsx)(n.P.span,{...s.X1,className:"inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-4",children:"Our Services"}),(0,i.jsxs)(n.P.h2,{...s.X1,className:"text-4xl md:text-5xl lg:text-7xl font-bold mb-6",children:[(0,i.jsx)("span",{className:"luxury-text-gradient",children:"Four Corners of"}),(0,i.jsx)("br",{}),(0,i.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-yellow-600 bg-clip-text text-transparent",children:"Excellence"})]}),(0,i.jsx)(n.P.p,{...s.X1,className:"text-lg md:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed",children:"Every vehicle receives our comprehensive four-corner approach: meticulous preparation, expert application, quality assurance, and ongoing protection. No detail overlooked, no corner cut."})]}),(0,i.jsx)(n.P.div,{...s.X1,className:"mb-16",children:(0,i.jsx)(l,{items:e,className:"max-w-6xl mx-auto"})}),(0,i.jsxs)(n.P.div,{...s.X1,className:"glass-card rounded-3xl p-8 md:p-12 max-w-5xl mx-auto luxury-shadow-lg border border-white/30",children:[(0,i.jsx)("h3",{className:"text-3xl md:text-4xl font-bold text-center mb-12",children:(0,i.jsx)("span",{className:"luxury-text-gradient",children:"Our Four-Corner Process"})}),(0,i.jsx)("div",{className:"grid md:grid-cols-4 gap-8",children:[{number:"01",title:"Assessment",description:"Thorough inspection and consultation",icon:"\uD83D\uDD0D"},{number:"02",title:"Preparation",description:"Meticulous cleaning and surface prep",icon:"\uD83E\uDDFD"},{number:"03",title:"Application",description:"Expert technique and premium products",icon:"✨"},{number:"04",title:"Protection",description:"Final inspection and care instructions",icon:"\uD83D\uDEE1️"}].map((e,t)=>(0,i.jsxs)(n.P.div,{...(0,s.F0)(.1*t),className:"text-center group",children:[(0,i.jsxs)("div",{className:"relative mb-6",children:[(0,i.jsx)("div",{className:"w-20 h-20 luxury-gradient rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto luxury-shadow group-hover:luxury-shadow-lg transition-all duration-300 group-hover:scale-105",children:e.number}),(0,i.jsx)("div",{className:"absolute -top-2 -right-2 text-2xl",children:e.icon})]}),(0,i.jsx)("h4",{className:"font-bold text-neutral-800 mb-3 text-lg",children:e.title}),(0,i.jsx)("p",{className:"text-neutral-600 leading-relaxed",children:e.description})]},t))})]})]})]})}},7504:(e,t,r)=>{"use strict";r.d(t,{y:()=>a});var i=r(3063),n=r(2742),s=r(1874);let a={test:e=>s.B.test(e)||i.u.test(e)||n.V.test(e),parse:e=>s.B.test(e)?s.B.parse(e):n.V.test(e)?n.V.parse(e):i.u.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?s.B.transform(e):n.V.transform(e),getAnimatableNone:e=>{let t=a.parse(e);return t.alpha=0,a.transform(t)}}},7529:(e,t,r)=>{"use strict";r.d(t,{O:()=>o,e:()=>a});var i=r(6570),n=r(567),s=r(1328);function a(e){return(0,i.N)(e.animate)||s._.some(t=>(0,n.w)(e[t]))}function o(e){return!!(a(e)||e.variants)}},7556:(e,t,r)=>{"use strict";function i(e,t){-1===e.indexOf(t)&&e.push(t)}function n(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}r.d(t,{Ai:()=>n,Kq:()=>i})},7606:(e,t,r)=>{"use strict";function i(e){return void 0===e||1===e}function n({scale:e,scaleX:t,scaleY:r}){return!i(e)||!i(t)||!i(r)}function s(e){return n(e)||a(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function a(e){var t,r;return(t=e.x)&&"0%"!==t||(r=e.y)&&"0%"!==r}r.d(t,{HD:()=>s,vF:()=>a,vk:()=>n})},7609:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});var i=r(5726),n=r(6633);function s(e,{layout:t,layoutId:r}){return i.f.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!n.H[e]||"opacity"===e)}},7690:(e,t,r)=>{"use strict";r.d(t,{X:()=>s});var i=r(7211),n=r(4948);function s(e,t=100,r){let a=r({...e,keyframes:[0,t]}),o=Math.min((0,n.t)(a),n.Y);return{type:"keyframes",ease:e=>a.next(o*e).value/t,duration:(0,i.X)(o)}}},7758:(e,t,r)=>{"use strict";r.d(t,{q:()=>i});let i=(e,t,r)=>r>t?t:r<e?e:r},7819:(e,t,r)=>{"use strict";r.d(t,{W:()=>i});let i={}},7886:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});let i=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase()},7947:()=>{},7992:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(2688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},8028:(e,t,r)=>{"use strict";r.d(t,{k:()=>i});let i=(e,t,r)=>e+(t-e)*r},8034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return n}});let i=r(4827);function n(e){let{re:t,groups:r}=e;return e=>{let n=t.exec(e);if(!n)return!1;let s=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new i.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[e,t]of Object.entries(r)){let r=n[t.pos];void 0!==r&&(t.repeat?a[e]=r.split("/").map(e=>s(e)):a[e]=s(r))}return a}}},8171:(e,t,r)=>{"use strict";r.d(t,{s:()=>n});var i=r(4479);function n(e){return(0,i.G)(e)&&"offsetHeight"in e}},8205:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});let i=(e,t)=>r=>t(e(r)),n=(...e)=>e.reduce(i)},8212:(e,t,r)=>{"use strict";function i(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:i}=r(6415);return i(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return i}})},8267:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var i=r(5723);function n(e){let t=[0];return(0,i.f)(t,e.length-1),t}},8304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return a},getExtensionRegexString:function(){return l},isMetadataPage:function(){return d},isMetadataRoute:function(){return h},isMetadataRouteFile:function(){return u},isStaticMetadataRoute:function(){return c}});let i=r(2958),n=r(4722),s=r(554),a={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function u(e,t,r){let n=(r?"":"?")+"$",s=`\\d?${r?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${n}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${n}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${n}`),RegExp(`[\\\\/]${a.icon.filename}${s}${l(a.icon.extensions,t)}${n}`),RegExp(`[\\\\/]${a.apple.filename}${s}${l(a.apple.extensions,t)}${n}`),RegExp(`[\\\\/]${a.openGraph.filename}${s}${l(a.openGraph.extensions,t)}${n}`),RegExp(`[\\\\/]${a.twitter.filename}${s}${l(a.twitter.extensions,t)}${n}`)],u=(0,i.normalizePathSep)(e);return o.some(e=>e.test(u))}function c(e){let t=e.replace(/\/route$/,"");return(0,s.isAppRouteRoute)(e)&&u(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function d(e){return!(0,s.isAppRouteRoute)(e)&&u(e,[],!1)}function h(e){let t=(0,n.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,s.isAppRouteRoute)(e)&&u(t,[],!1)}},8337:(e,t,r)=>{"use strict";function i(e){let t=[{},{}];return e?.values.forEach((e,r)=>{t[0][r]=e.get(),t[1][r]=e.getVelocity()}),t}function n(e,t,r,n){if("function"==typeof t){let[s,a]=i(n);t=t(void 0!==r?r:e.custom,s,a)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[s,a]=i(n);t=t(void 0!==r?r:e.custom,s,a)}return t}r.d(t,{a:()=>n})},8340:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(2688).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},8347:(e,t,r)=>{"use strict";r.d(t,{K:()=>i});let i=(e,t,r=10)=>{let i="",n=Math.max(Math.round(t/r),2);for(let t=0;t<n;t++)i+=Math.round(1e4*e(t/(n-1)))/1e4+", ";return`linear(${i.substring(0,i.length-2)})`}},8605:(e,t,r)=>{"use strict";r.d(t,{x:()=>a});var i=r(5927),n=r(5726),s=r(5934);function a(e,t,r){let a=(0,s.x)(e,t,r);for(let r in e)((0,i.S)(e[r])||(0,i.S)(t[r]))&&(a[-1!==n.U.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]);return a}},8744:(e,t,r)=>{"use strict";r.d(t,{O:()=>u});var i=r(5726),n=r(2238);let s=(e,t)=>t&&"number"==typeof e?t.transform(e):e;var a=r(748);let o={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},l=i.U.length;function u(e,t,r){let{style:u,vars:c,transformOrigin:d}=e,h=!1,p=!1;for(let e in t){let r=t[e];if(i.f.has(e)){h=!0;continue}if((0,n.j)(e)){c[e]=r;continue}{let t=s(r,a.W[e]);e.startsWith("origin")?(p=!0,d[e]=t):u[e]=t}}if(!t.transform&&(h||r?u.transform=function(e,t,r){let n="",u=!0;for(let c=0;c<l;c++){let l=i.U[c],d=e[l];if(void 0===d)continue;let h=!0;if(!(h="number"==typeof d?d===+!!l.startsWith("scale"):0===parseFloat(d))||r){let e=s(d,a.W[l]);if(!h){u=!1;let t=o[l]||l;n+=`${t}(${e}) `}r&&(t[l]=e)}}return n=n.trim(),r?n=r(t,u?"":n):u&&(n="none"),n}(t,e.transform,r):u.transform&&(u.transform="none")),p){let{originX:e="50%",originY:t="50%",originZ:r=0}=d;u.transformOrigin=`${e} ${t} ${r}`}}},8762:(e,t,r)=>{"use strict";r.d(t,{S:()=>i});let i=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},8778:(e,t,r)=>{"use strict";r.d(t,{l:()=>p});var i=r(5726),n=r(7146),s=r(4538),a=r(1048),o=r(7886),l=r(2702);let u=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);var c=r(9197),d=r(3088),h=r(8605);class p extends a.b{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=s.ge}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(i.f.has(t)){let e=(0,n.D)(t);return e&&e.default||0}return t=u.has(t)?t:(0,o.I)(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,r){return(0,h.x)(e,t,r)}build(e,t,r){(0,l.B)(e,t,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(e,t,r,i){for(let r in(0,d.e)(e,t,void 0,i),t.attrs)e.setAttribute(u.has(r)?r:(0,o.I)(r),t.attrs[r])}mount(e){this.isSVGTag=(0,c.n)(e.tagName),super.mount(e)}}},8830:(e,t,r)=>{"use strict";r.d(t,{G:()=>i});let i=e=>t=>1-e(1-t)},8920:(e,t,r)=>{"use strict";r.d(t,{N:()=>v});var i=r(687),n=r(3210),s=r(2157),a=r(2789),o=r(2743),l=r(1279),u=r(8171),c=r(2582);class d extends n.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,u.s)(e)&&e.offsetWidth||0,i=this.props.sizeRef.current;i.height=t.offsetHeight||0,i.width=t.offsetWidth||0,i.top=t.offsetTop,i.left=t.offsetLeft,i.right=r-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h({children:e,isPresent:t,anchorX:r}){let s=(0,n.useId)(),a=(0,n.useRef)(null),o=(0,n.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,n.useContext)(c.Q);return(0,n.useInsertionEffect)(()=>{let{width:e,height:i,top:n,left:u,right:c}=o.current;if(t||!a.current||!e||!i)return;let d="left"===r?`left: ${u}`:`right: ${c}`;a.current.dataset.motionPopId=s;let h=document.createElement("style");return l&&(h.nonce=l),document.head.appendChild(h),h.sheet&&h.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${i}px !important;
            ${d}px !important;
            top: ${n}px !important;
          }
        `),()=>{document.head.contains(h)&&document.head.removeChild(h)}},[t]),(0,i.jsx)(d,{isPresent:t,childRef:a,sizeRef:o,children:n.cloneElement(e,{ref:a})})}let p=({children:e,initial:t,isPresent:r,onExitComplete:s,custom:o,presenceAffectsLayout:u,mode:c,anchorX:d})=>{let p=(0,a.M)(m),f=(0,n.useId)(),g=!0,x=(0,n.useMemo)(()=>(g=!1,{id:f,initial:t,isPresent:r,custom:o,onExitComplete:e=>{for(let t of(p.set(e,!0),p.values()))if(!t)return;s&&s()},register:e=>(p.set(e,!1),()=>p.delete(e))}),[r,p,s]);return u&&g&&(x={...x}),(0,n.useMemo)(()=>{p.forEach((e,t)=>p.set(t,!1))},[r]),n.useEffect(()=>{r||p.size||!s||s()},[r]),"popLayout"===c&&(e=(0,i.jsx)(h,{isPresent:r,anchorX:d,children:e})),(0,i.jsx)(l.t.Provider,{value:x,children:e})};function m(){return new Map}var f=r(6044);let g=e=>e.key||"";function x(e){let t=[];return n.Children.forEach(e,e=>{(0,n.isValidElement)(e)&&t.push(e)}),t}let v=({children:e,custom:t,initial:r=!0,onExitComplete:l,presenceAffectsLayout:u=!0,mode:c="sync",propagate:d=!1,anchorX:h="left"})=>{let[m,v]=(0,f.xQ)(d),y=(0,n.useMemo)(()=>x(e),[e]),b=d&&!m?[]:y.map(g),w=(0,n.useRef)(!0),j=(0,n.useRef)(y),P=(0,a.M)(()=>new Map),[k,A]=(0,n.useState)(y),[T,E]=(0,n.useState)(y);(0,o.E)(()=>{w.current=!1,j.current=y;for(let e=0;e<T.length;e++){let t=g(T[e]);b.includes(t)?P.delete(t):!0!==P.get(t)&&P.set(t,!1)}},[T,b.length,b.join("-")]);let N=[];if(y!==k){let e=[...y];for(let t=0;t<T.length;t++){let r=T[t],i=g(r);b.includes(i)||(e.splice(t,0,r),N.push(r))}return"wait"===c&&N.length&&(e=N),E(x(e)),A(y),null}let{forceRender:S}=(0,n.useContext)(s.L);return(0,i.jsx)(i.Fragment,{children:T.map(e=>{let n=g(e),s=(!d||!!m)&&(y===T||b.includes(n));return(0,i.jsx)(p,{isPresent:s,initial:(!w.current||!!r)&&void 0,custom:t,presenceAffectsLayout:u,mode:c,onExitComplete:s?void 0:()=>{if(!P.has(n))return;P.set(n,!0);let e=!0;P.forEach(t=>{t||(e=!1)}),e&&(S?.(),E(j.current),d&&v?.(),l&&l())},anchorX:h,children:e},n)})})}},9052:(e,t,r)=>{"use strict";r.d(t,{Footer:()=>i});let i=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\navigation\\footer.tsx","Footer")},9076:(e,t,r)=>{"use strict";r.d(t,{E4:()=>o,Hr:()=>d,W9:()=>c});var i=r(9602),n=r(5726),s=r(5444),a=r(2874);let o=e=>e===s.ai||e===a.px,l=new Set(["x","y","z"]),u=n.U.filter(e=>!l.has(e));function c(e){let t=[];return u.forEach(r=>{let i=e.getValue(r);void 0!==i&&(t.push([r,i.get()]),i.set(+!!r.startsWith("scale")))}),t}let d={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>(0,i.ry)(t,"x"),y:(e,{transform:t})=>(0,i.ry)(t,"y")};d.translateX=d.x,d.translateY=d.y},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9132:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>h,tree:()=>u});var i=r(5239),n=r(8088),s=r(8170),a=r.n(s),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\app\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},h=new i.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},9197:(e,t,r)=>{"use strict";r.d(t,{n:()=>i});let i=e=>"string"==typeof e&&"svg"===e.toLowerCase()},9240:(e,t,r)=>{"use strict";r.d(t,{B:()=>n});let i={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},n={};for(let e in i)n[e]={isEnabled:t=>i[e].some(e=>!!t[e])}},9292:(e,t,r)=>{"use strict";function i(e,t,r){if(e instanceof EventTarget)return[e];if("string"==typeof e){let i=document;t&&(i=t.current);let n=r?.[e]??i.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}r.d(t,{K:()=>i})},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9527:(e,t,r)=>{"use strict";r.d(t,{h:()=>i});let i=e=>Array.isArray(e)&&"number"!=typeof e[0]},9542:(e,t,r)=>{"use strict";r.d(t,{B:()=>N});var i=r(736),n=r(4325),s=r(3671),a=r(5927),o=r(5726),l=r(4342),u=r(7504),c=r(9664),d=r(5472),h=r(1888);let p=[...d.T,u.y,c.f],m=e=>p.find((0,h.w)(e));var f=r(9837),g=r(4278),x=r(6954),v=r(4296),y=r(9240),b=r(4538),w=r(7044);let j={current:null},P={current:!1};var k=r(2699),A=r(7529),T=r(8337);let E=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class N{scrapeMotionValuesFromProps(e,t,r){return{}}constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:o,blockInitialAnimation:l,visualState:u},c={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=i.h,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=n.k.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,s.Gt.render(this.render,!1,!0))};let{latestValues:d,renderState:h}=u;this.latestValues=d,this.baseTarget={...d},this.initialValues=t.initial?{...d}:{},this.renderState=h,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=o,this.options=c,this.blockInitialAnimation=!!l,this.isControllingVariants=(0,A.e)(t),this.isVariantNode=(0,A.O)(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:p,...m}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in m){let t=m[e];void 0!==d[e]&&(0,a.S)(t)&&t.set(d[e],!1)}}mount(e){this.current=e,k.C.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),P.current||function(){if(P.current=!0,w.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>j.current=e.matches;e.addListener(t),t()}else j.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||j.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),(0,s.WG)(this.notifyUpdate),(0,s.WG)(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let r;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let i=o.f.has(e);i&&this.onBindTransform&&this.onBindTransform();let n=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&s.Gt.preRender(this.notifyUpdate),i&&this.projection&&(this.projection.isTransformDirty=!0)}),a=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{n(),a(),r&&r(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in y.B){let t=y.B[e];if(!t)continue;let{isEnabled:r,Feature:i}=t;if(!this.features[e]&&i&&r(this.props)&&(this.features[e]=new i(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):(0,b.ge)()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<E.length;t++){let r=E[t];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let i=e["on"+r];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=function(e,t,r){for(let i in t){let n=t[i],s=r[i];if((0,a.S)(n))e.addValue(i,n);else if((0,a.S)(s))e.addValue(i,(0,l.OQ)(n,{owner:e}));else if(s!==n)if(e.hasValue(i)){let t=e.getValue(i);!0===t.liveStyle?t.jump(n):t.hasAnimated||t.set(n)}else{let t=e.getStaticValue(i);e.addValue(i,(0,l.OQ)(void 0!==t?t:n,{owner:e}))}}for(let i in r)void 0===t[i]&&e.removeValue(i);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let r=this.values.get(e);t!==r&&(r&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=(0,l.OQ)(null===t?void 0:t,{owner:this}),this.addValue(e,r)),r}readValue(e,t){let r=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=r&&("string"==typeof r&&((0,g.i)(r)||(0,x.$)(r))?r=parseFloat(r):!m(r)&&c.f.test(t)&&(r=(0,f.J)(e,t)),this.setBaseTarget(e,(0,a.S)(r)?r.get():r)),(0,a.S)(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let i=(0,T.a)(this.props,r,this.presenceContext?.custom);i&&(t=i[e])}if(r&&void 0!==t)return t;let i=this.getBaseTargetFromProps(this.props,e);return void 0===i||(0,a.S)(i)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new v.v),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}},9551:e=>{"use strict";e.exports=require("url")},9579:(e,t,r)=>{"use strict";r.d(t,{Services:()=>i});let i=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Services() from the server but Services is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Client websites\\Jon Four corner\\jon-four-corners-detailing\\src\\components\\sections\\services.tsx","Services")},9602:(e,t,r)=>{"use strict";r.d(t,{Ib:()=>h,ry:()=>d,zs:()=>c});let i=e=>180*e/Math.PI,n=e=>a(i(Math.atan2(e[1],e[0]))),s={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:n,rotateZ:n,skewX:e=>i(Math.atan(e[1])),skewY:e=>i(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},a=e=>((e%=360)<0&&(e+=360),e),o=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),l=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),u={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:o,scaleY:l,scale:e=>(o(e)+l(e))/2,rotateX:e=>a(i(Math.atan2(e[6],e[5]))),rotateY:e=>a(i(Math.atan2(-e[2],e[0]))),rotateZ:n,rotate:n,skewX:e=>i(Math.atan(e[4])),skewY:e=>i(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function c(e){return+!!e.includes("scale")}function d(e,t){let r,i;if(!e||"none"===e)return c(t);let n=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)r=u,i=n;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=s,i=t}if(!i)return c(t);let a=r[t],o=i[1].split(",").map(p);return"function"==typeof a?a(o):o[a]}let h=(e,t)=>{let{transform:r="none"}=getComputedStyle(e);return d(r,t)};function p(e){return parseFloat(e.trim())}},9664:(e,t,r)=>{"use strict";r.d(t,{V:()=>c,f:()=>m});var i=r(7504);let n=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var s=r(8762),a=r(7095);let o="number",l="color",u=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function c(e){let t=e.toString(),r=[],n={color:[],number:[],var:[]},s=[],a=0,c=t.replace(u,e=>(i.y.test(e)?(n.color.push(a),s.push(l),r.push(i.y.parse(e))):e.startsWith("var(")?(n.var.push(a),s.push("var"),r.push(e)):(n.number.push(a),s.push(o),r.push(parseFloat(e))),++a,"${}")).split("${}");return{values:r,split:c,indexes:n,types:s}}function d(e){return c(e).values}function h(e){let{split:t,types:r}=c(e),n=t.length;return e=>{let s="";for(let u=0;u<n;u++)if(s+=t[u],void 0!==e[u]){let t=r[u];t===o?s+=(0,a.a)(e[u]):t===l?s+=i.y.transform(e[u]):s+=e[u]}return s}}let p=e=>"number"==typeof e?0:i.y.test(e)?i.y.getAnimatableNone(e):e,m={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(s.S)?.length||0)+(e.match(n)?.length||0)>0},parse:d,createTransformer:h,getAnimatableNone:function(e){let t=d(e);return h(e)(t.map(p))}}},9831:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9837:(e,t,r)=>{"use strict";r.d(t,{J:()=>a});var i=r(9664),n=r(5485),s=r(7146);function a(e,t){let r=(0,s.D)(e);return r!==n.p&&(r=i.f),r.getAnimatableNone?r.getAnimatableNone(t):void 0}},9848:(e,t,r)=>{"use strict";r.d(t,{I:()=>a});var i=r(7819);let n=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var s=r(2082);function a(e,t){let r=!1,a=!0,o={delta:0,timestamp:0,isProcessing:!1},l=()=>r=!0,u=n.reduce((e,r)=>(e[r]=function(e,t){let r=new Set,i=new Set,n=!1,a=!1,o=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},u=0;function c(t){o.has(t)&&(d.schedule(t),e()),u++,t(l)}let d={schedule:(e,t=!1,s=!1)=>{let a=s&&n?r:i;return t&&o.add(e),a.has(e)||a.add(e),e},cancel:e=>{i.delete(e),o.delete(e)},process:e=>{if(l=e,n){a=!0;return}n=!0,[r,i]=[i,r],r.forEach(c),t&&s.Q.value&&s.Q.value.frameloop[t].push(u),u=0,r.clear(),n=!1,a&&(a=!1,d.process(e))}};return d}(l,t?r:void 0),e),{}),{setup:c,read:d,resolveKeyframes:h,preUpdate:p,update:m,preRender:f,render:g,postRender:x}=u,v=()=>{let n=i.W.useManualTiming?o.timestamp:performance.now();r=!1,i.W.useManualTiming||(o.delta=a?1e3/60:Math.max(Math.min(n-o.timestamp,40),1)),o.timestamp=n,o.isProcessing=!0,c.process(o),d.process(o),h.process(o),p.process(o),m.process(o),f.process(o),g.process(o),x.process(o),o.isProcessing=!1,r&&t&&(a=!1,e(v))},y=()=>{r=!0,a=!0,o.isProcessing||e(v)};return{schedule:n.reduce((e,t)=>{let i=u[t];return e[t]=(e,t=!1,n=!1)=>(r||y(),i.schedule(e,t,n)),e},{}),cancel:e=>{for(let t=0;t<n.length;t++)u[n[t]].cancel(e)},state:o,steps:u}}},9891:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(2688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,162],()=>r(9132));module.exports=i})();