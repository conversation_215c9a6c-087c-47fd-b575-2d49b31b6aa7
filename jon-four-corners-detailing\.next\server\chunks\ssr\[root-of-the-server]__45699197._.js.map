{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Animation utilities\nexport const fadeInUp = {\n  initial: { opacity: 0, y: 60 },\n  animate: { opacity: 1, y: 0 },\n  transition: { duration: 0.6, ease: \"easeOut\" }\n}\n\nexport const fadeInLeft = {\n  initial: { opacity: 0, x: -60 },\n  animate: { opacity: 1, x: 0 },\n  transition: { duration: 0.6, ease: \"easeOut\" }\n}\n\nexport const fadeInRight = {\n  initial: { opacity: 0, x: 60 },\n  animate: { opacity: 1, x: 0 },\n  transition: { duration: 0.6, ease: \"easeOut\" }\n}\n\nexport const staggerContainer = {\n  initial: {},\n  animate: {\n    transition: {\n      staggerChildren: 0.1,\n      delayChildren: 0.3\n    }\n  }\n}\n\nexport const scaleIn = {\n  initial: { opacity: 0, scale: 0.8 },\n  animate: { opacity: 1, scale: 1 },\n  transition: { duration: 0.5, ease: \"easeOut\" }\n}\n\n// Scroll animation utilities\nexport const scrollFadeIn = {\n  initial: { opacity: 0, y: 50 },\n  whileInView: { opacity: 1, y: 0 },\n  transition: { duration: 0.8, ease: \"easeOut\" },\n  viewport: { once: true, margin: \"-100px\" }\n}\n\n// Luxury brand color palette\nexport const colors = {\n  primary: {\n    50: '#f0f9ff',\n    100: '#e0f2fe',\n    200: '#bae6fd',\n    300: '#7dd3fc',\n    400: '#38bdf8',\n    500: '#0ea5e9',\n    600: '#0284c7',\n    700: '#0369a1',\n    800: '#075985',\n    900: '#0c4a6e',\n  },\n  accent: {\n    50: '#fefce8',\n    100: '#fef9c3',\n    200: '#fef08a',\n    300: '#fde047',\n    400: '#facc15',\n    500: '#eab308',\n    600: '#ca8a04',\n    700: '#a16207',\n    800: '#854d0e',\n    900: '#713f12',\n  },\n  neutral: {\n    50: '#fafafa',\n    100: '#f5f5f5',\n    200: '#e5e5e5',\n    300: '#d4d4d4',\n    400: '#a3a3a3',\n    500: '#737373',\n    600: '#525252',\n    700: '#404040',\n    800: '#262626',\n    900: '#171717',\n  }\n}\n\n// Typography utilities\nexport const typography = {\n  heading: {\n    h1: \"text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight\",\n    h2: \"text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight\",\n    h3: \"text-2xl md:text-3xl lg:text-4xl font-semibold tracking-tight\",\n    h4: \"text-xl md:text-2xl lg:text-3xl font-semibold tracking-tight\",\n  },\n  body: {\n    large: \"text-lg md:text-xl leading-relaxed\",\n    base: \"text-base md:text-lg leading-relaxed\",\n    small: \"text-sm md:text-base leading-relaxed\",\n  }\n}\n\n// Responsive breakpoints\nexport const breakpoints = {\n  sm: '640px',\n  md: '768px',\n  lg: '1024px',\n  xl: '1280px',\n  '2xl': '1536px',\n}\n\n// Luxury animation presets\nexport const luxuryAnimations = {\n  gentleFloat: {\n    animate: {\n      y: [-10, 10, -10],\n      transition: {\n        duration: 6,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      }\n    }\n  },\n  shimmer: {\n    animate: {\n      backgroundPosition: [\"0% 50%\", \"100% 50%\", \"0% 50%\"],\n      transition: {\n        duration: 3,\n        repeat: Infinity,\n        ease: \"linear\"\n      }\n    }\n  },\n  luxuryHover: {\n    whileHover: {\n      scale: 1.05,\n      transition: { duration: 0.3, ease: \"easeOut\" }\n    },\n    whileTap: {\n      scale: 0.95,\n      transition: { duration: 0.1 }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,WAAW;IACtB,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,YAAY;QAAE,UAAU;QAAK,MAAM;IAAU;AAC/C;AAEO,MAAM,aAAa;IACxB,SAAS;QAAE,SAAS;QAAG,GAAG,CAAC;IAAG;IAC9B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,YAAY;QAAE,UAAU;QAAK,MAAM;IAAU;AAC/C;AAEO,MAAM,cAAc;IACzB,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,YAAY;QAAE,UAAU;QAAK,MAAM;IAAU;AAC/C;AAEO,MAAM,mBAAmB;IAC9B,SAAS,CAAC;IACV,SAAS;QACP,YAAY;YACV,iBAAiB;YACjB,eAAe;QACjB;IACF;AACF;AAEO,MAAM,UAAU;IACrB,SAAS;QAAE,SAAS;QAAG,OAAO;IAAI;IAClC,SAAS;QAAE,SAAS;QAAG,OAAO;IAAE;IAChC,YAAY;QAAE,UAAU;QAAK,MAAM;IAAU;AAC/C;AAGO,MAAM,eAAe;IAC1B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,aAAa;QAAE,SAAS;QAAG,GAAG;IAAE;IAChC,YAAY;QAAE,UAAU;QAAK,MAAM;IAAU;IAC7C,UAAU;QAAE,MAAM;QAAM,QAAQ;IAAS;AAC3C;AAGO,MAAM,SAAS;IACpB,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,QAAQ;QACN,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAGO,MAAM,aAAa;IACxB,SAAS;QACP,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IACA,MAAM;QACJ,OAAO;QACP,MAAM;QACN,OAAO;IACT;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT;AAGO,MAAM,mBAAmB;IAC9B,aAAa;QACX,SAAS;YACP,GAAG;gBAAC,CAAC;gBAAI;gBAAI,CAAC;aAAG;YACjB,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IACA,SAAS;QACP,SAAS;YACP,oBAAoB;gBAAC;gBAAU;gBAAY;aAAS;YACpD,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IACA,aAAa;QACX,YAAY;YACV,OAAO;YACP,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;QAC/C;QACA,UAAU;YACR,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;AACF", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/ui/hover-border-gradient.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { motion } from \"motion/react\";\nimport { cn } from \"@/lib/utils\";\n\ntype Direction = \"TOP\" | \"LEFT\" | \"BOTTOM\" | \"RIGHT\";\n\nexport function HoverBorderGradient({\n  children,\n  containerClassName,\n  className,\n  as: Tag = \"button\",\n  duration = 1,\n  clockwise = true,\n  ...props\n}: React.PropsWithChildren<\n  {\n    as?: React.ElementType;\n    containerClassName?: string;\n    className?: string;\n    duration?: number;\n    clockwise?: boolean;\n  } & React.HTMLAttributes<HTMLElement>\n>) {\n  const [hovered, setHovered] = useState<boolean>(false);\n  const [direction, setDirection] = useState<Direction>(\"TOP\");\n\n  const rotateDirection = (currentDirection: Direction): Direction => {\n    const directions: Direction[] = [\"TOP\", \"LEFT\", \"BOTTOM\", \"RIGHT\"];\n    const currentIndex = directions.indexOf(currentDirection);\n    const nextIndex = clockwise\n      ? (currentIndex - 1 + directions.length) % directions.length\n      : (currentIndex + 1) % directions.length;\n    return directions[nextIndex];\n  };\n\n  const movingMap: Record<Direction, string> = {\n    TOP: \"radial-gradient(20.7% 50% at 50% 0%, hsl(0, 0%, 100%) 0%, rgba(255, 255, 255, 0) 100%)\",\n    LEFT: \"radial-gradient(16.6% 43.1% at 0% 50%, hsl(0, 0%, 100%) 0%, rgba(255, 255, 255, 0) 100%)\",\n    BOTTOM:\n      \"radial-gradient(20.7% 50% at 50% 100%, hsl(0, 0%, 100%) 0%, rgba(255, 255, 255, 0) 100%)\",\n    RIGHT:\n      \"radial-gradient(16.2% 41.199999999999996% at 100% 50%, hsl(0, 0%, 100%) 0%, rgba(255, 255, 255, 0) 100%)\",\n  };\n\n  const highlight =\n    \"radial-gradient(75% 181.15942028985506% at 50% 50%, #0ea5e9 0%, rgba(255, 255, 255, 0) 100%)\";\n\n  useEffect(() => {\n    if (!hovered) {\n      const interval = setInterval(() => {\n        setDirection((prevState) => rotateDirection(prevState));\n      }, duration * 1000);\n      return () => clearInterval(interval);\n    }\n  }, [hovered]);\n  \n  return (\n    <Tag\n      onMouseEnter={(event: React.MouseEvent<HTMLDivElement>) => {\n        setHovered(true);\n      }}\n      onMouseLeave={() => setHovered(false)}\n      className={cn(\n        \"relative flex rounded-full border content-center bg-black/20 hover:bg-black/10 transition duration-500 dark:bg-white/20 items-center flex-col flex-nowrap gap-10 h-min justify-center overflow-visible p-px decoration-clone w-fit\",\n        containerClassName\n      )}\n      {...props}\n    >\n      <div\n        className={cn(\n          \"w-auto text-white z-10 bg-black px-4 py-2 rounded-[inherit]\",\n          className\n        )}\n      >\n        {children}\n      </div>\n      <motion.div\n        className={cn(\n          \"flex-none inset-0 overflow-hidden absolute z-0 rounded-[inherit]\"\n        )}\n        style={{\n          filter: \"blur(2px)\",\n          position: \"absolute\",\n          width: \"100%\",\n          height: \"100%\",\n        }}\n        initial={{ background: movingMap[direction] }}\n        animate={{\n          background: hovered\n            ? [movingMap[direction], highlight]\n            : movingMap[direction],\n        }}\n        transition={{ ease: \"linear\", duration: duration ?? 1 }}\n      />\n      <div className=\"bg-black absolute z-1 flex-none inset-[2px] rounded-[100px]\" />\n    </Tag>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAQO,SAAS,oBAAoB,EAClC,QAAQ,EACR,kBAAkB,EAClB,SAAS,EACT,IAAI,MAAM,QAAQ,EAClB,WAAW,CAAC,EACZ,YAAY,IAAI,EAChB,GAAG,OASJ;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IAEtD,MAAM,kBAAkB,CAAC;QACvB,MAAM,aAA0B;YAAC;YAAO;YAAQ;YAAU;SAAQ;QAClE,MAAM,eAAe,WAAW,OAAO,CAAC;QACxC,MAAM,YAAY,YACd,CAAC,eAAe,IAAI,WAAW,MAAM,IAAI,WAAW,MAAM,GAC1D,CAAC,eAAe,CAAC,IAAI,WAAW,MAAM;QAC1C,OAAO,UAAU,CAAC,UAAU;IAC9B;IAEA,MAAM,YAAuC;QAC3C,KAAK;QACL,MAAM;QACN,QACE;QACF,OACE;IACJ;IAEA,MAAM,YACJ;IAEF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;YACZ,MAAM,WAAW,YAAY;gBAC3B,aAAa,CAAC,YAAc,gBAAgB;YAC9C,GAAG,WAAW;YACd,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;KAAQ;IAEZ,qBACE,8OAAC;QACC,cAAc,CAAC;YACb,WAAW;QACb;QACA,cAAc,IAAM,WAAW;QAC/B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;0BAGD;;;;;;0BAEH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;gBAEF,OAAO;oBACL,QAAQ;oBACR,UAAU;oBACV,OAAO;oBACP,QAAQ;gBACV;gBACA,SAAS;oBAAE,YAAY,SAAS,CAAC,UAAU;gBAAC;gBAC5C,SAAS;oBACP,YAAY,UACR;wBAAC,SAAS,CAAC,UAAU;wBAAE;qBAAU,GACjC,SAAS,CAAC,UAAU;gBAC1B;gBACA,YAAY;oBAAE,MAAM;oBAAU,UAAU,YAAY;gBAAE;;;;;;0BAExD,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/navigation/navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"motion/react\";\nimport { Menu, X, Phone, MapPin } from \"lucide-react\";\nimport { HoverBorderGradient } from \"@/components/ui/hover-border-gradient\";\n\nexport function Navbar() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  const navItems = [\n    { name: \"Services\", href: \"#services\" },\n    { name: \"Process\", href: \"#process\" },\n    { name: \"Gallery\", href: \"#gallery\" },\n    { name: \"About\", href: \"#about\" },\n    { name: \"Contact\", href: \"#contact\" },\n  ];\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.6, ease: \"easeOut\" }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        scrolled\n          ? \"bg-white/90 backdrop-blur-md shadow-lg border-b border-neutral-200/50\"\n          : \"bg-transparent\"\n      }`}\n    >\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16 md:h-20\">\n          {/* Logo */}\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"flex items-center space-x-2\"\n          >\n            <div className=\"w-10 h-10 bg-gradient-to-r from-blue-600 to-yellow-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">J</span>\n            </div>\n            <div className=\"hidden sm:block\">\n              <h1 className=\"text-xl font-bold text-neutral-800\">\n                Jon Four Corners\n              </h1>\n              <p className=\"text-xs text-neutral-600 -mt-1\">Detailing</p>\n            </div>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <motion.a\n                key={item.name}\n                href={item.href}\n                whileHover={{ y: -2 }}\n                className=\"text-neutral-700 hover:text-blue-600 transition-colors duration-200 font-medium\"\n              >\n                {item.name}\n              </motion.a>\n            ))}\n          </div>\n\n          {/* Contact Info & CTA */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-4 text-sm text-neutral-600\">\n              <div className=\"flex items-center space-x-1\">\n                <Phone className=\"w-4 h-4\" />\n                <span>(*************</span>\n              </div>\n              <div className=\"flex items-center space-x-1\">\n                <MapPin className=\"w-4 h-4\" />\n                <span>Four Corners Area</span>\n              </div>\n            </div>\n            \n            <HoverBorderGradient\n              containerClassName=\"rounded-full\"\n              className=\"bg-transparent text-neutral-800 px-6 py-2 text-sm font-semibold\"\n            >\n              Get Quote\n            </HoverBorderGradient>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <motion.button\n            whileTap={{ scale: 0.95 }}\n            onClick={() => setIsOpen(!isOpen)}\n            className=\"md:hidden p-2 rounded-lg bg-neutral-100 text-neutral-700\"\n          >\n            {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n          </motion.button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"md:hidden bg-white/95 backdrop-blur-md border-t border-neutral-200/50\"\n          >\n            <div className=\"container mx-auto px-4 py-4\">\n              <div className=\"flex flex-col space-y-4\">\n                {navItems.map((item, index) => (\n                  <motion.a\n                    key={item.name}\n                    href={item.href}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    onClick={() => setIsOpen(false)}\n                    className=\"text-neutral-700 hover:text-blue-600 transition-colors duration-200 font-medium py-2\"\n                  >\n                    {item.name}\n                  </motion.a>\n                ))}\n                \n                <div className=\"pt-4 border-t border-neutral-200\">\n                  <div className=\"flex flex-col space-y-2 text-sm text-neutral-600 mb-4\">\n                    <div className=\"flex items-center space-x-2\">\n                      <Phone className=\"w-4 h-4\" />\n                      <span>(*************</span>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <MapPin className=\"w-4 h-4\" />\n                      <span>Four Corners Area</span>\n                    </div>\n                  </div>\n                  \n                  <HoverBorderGradient\n                    containerClassName=\"rounded-full w-full\"\n                    className=\"bg-transparent text-neutral-800 px-6 py-3 text-sm font-semibold w-full text-center\"\n                  >\n                    Get Free Quote\n                  </HoverBorderGradient>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAOO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,WAAW,CAAC,4DAA4D,EACtE,WACI,0EACA,kBACJ;;0BAEF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDAGnD,8OAAC;4CAAE,WAAU;sDAAiC;;;;;;;;;;;;;;;;;;sCAKlD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,WAAU;8CAET,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;;;;;;sCAWpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAIV,8OAAC,uJAAA,CAAA,sBAAmB;oCAClB,oBAAmB;oCACnB,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,UAAU;gCAAE,OAAO;4BAAK;4BACxB,SAAS,IAAM,UAAU,CAAC;4BAC1B,WAAU;sCAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM5D,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,KAAK,IAAI;wCACf,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,QAAQ;wCAAI;wCACjC,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAET,KAAK,IAAI;uCARL,KAAK,IAAI;;;;;8CAYlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;sDAIV,8OAAC,uJAAA,CAAA,sBAAmB;4CAClB,oBAAmB;4CACnB,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/ui/text-generate-effect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { motion, stagger, useAnimate } from \"motion/react\";\nimport { cn } from \"@/lib/utils\";\n\nexport const TextGenerateEffect = ({\n  words,\n  className,\n  filter = true,\n  duration = 0.5,\n}: {\n  words: string;\n  className?: string;\n  filter?: boolean;\n  duration?: number;\n}) => {\n  const [scope, animate] = useAnimate();\n  let wordsArray = words.split(\" \");\n  \n  useEffect(() => {\n    animate(\n      \"span\",\n      {\n        opacity: 1,\n        filter: filter ? \"blur(0px)\" : \"none\",\n      },\n      {\n        duration: duration ? duration : 1,\n        delay: stagger(0.2),\n      }\n    );\n  }, [scope.current]);\n\n  const renderWords = () => {\n    return (\n      <motion.div ref={scope}>\n        {wordsArray.map((word, idx) => {\n          return (\n            <motion.span\n              key={word + idx}\n              className=\"dark:text-white text-black opacity-0\"\n              style={{\n                filter: filter ? \"blur(10px)\" : \"none\",\n              }}\n            >\n              {word}{\" \"}\n            </motion.span>\n          );\n        })}\n      </motion.div>\n    );\n  };\n\n  return (\n    <div className={cn(\"font-bold\", className)}>\n      <div className=\"mt-4\">\n        <div className=\"dark:text-white text-black text-2xl leading-snug tracking-wide\">\n          {renderWords()}\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAMO,MAAM,qBAAqB,CAAC,EACjC,KAAK,EACL,SAAS,EACT,SAAS,IAAI,EACb,WAAW,GAAG,EAMf;IACC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAA,GAAA,uLAAA,CAAA,aAAU,AAAD;IAClC,IAAI,aAAa,MAAM,KAAK,CAAC;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QACE,QACA;YACE,SAAS;YACT,QAAQ,SAAS,cAAc;QACjC,GACA;YACE,UAAU,WAAW,WAAW;YAChC,OAAO,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE;QACjB;IAEJ,GAAG;QAAC,MAAM,OAAO;KAAC;IAElB,MAAM,cAAc;QAClB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAAC,KAAK;sBACd,WAAW,GAAG,CAAC,CAAC,MAAM;gBACrB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBAEV,WAAU;oBACV,OAAO;wBACL,QAAQ,SAAS,eAAe;oBAClC;;wBAEC;wBAAM;;mBANF,OAAO;;;;;YASlB;;;;;;IAGN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC9B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/ui/background-gradient.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport React from \"react\";\nimport { motion } from \"motion/react\";\n\nexport const BackgroundGradient = ({\n  children,\n  className,\n  containerClassName,\n  animate = true,\n}: {\n  children?: React.ReactNode;\n  className?: string;\n  containerClassName?: string;\n  animate?: boolean;\n}) => {\n  const variants = {\n    initial: {\n      backgroundPosition: \"0 50%\",\n    },\n    animate: {\n      backgroundPosition: [\"0, 50%\", \"100% 50%\", \"0 50%\"],\n    },\n  };\n  \n  return (\n    <div className={cn(\"relative p-[4px] group\", containerClassName)}>\n      <motion.div\n        variants={animate ? variants : undefined}\n        initial={animate ? \"initial\" : undefined}\n        animate={animate ? \"animate\" : undefined}\n        transition={\n          animate\n            ? {\n                duration: 5,\n                repeat: Infinity,\n                repeatType: \"reverse\",\n              }\n            : undefined\n        }\n        style={{\n          backgroundSize: animate ? \"400% 400%\" : undefined,\n        }}\n        className={cn(\n          \"absolute inset-0 rounded-3xl z-[1] opacity-60 group-hover:opacity-100 blur-xl transition duration-500 will-change-transform\",\n          \"bg-[radial-gradient(circle_farthest-side_at_0_100%,#0ea5e9,transparent),radial-gradient(circle_farthest-side_at_100%_0,#eab308,transparent),radial-gradient(circle_farthest-side_at_100%_100%,#0284c7,transparent),radial-gradient(circle_farthest-side_at_0_0,#38bdf8,#141316)]\"\n        )}\n      />\n      <motion.div\n        variants={animate ? variants : undefined}\n        initial={animate ? \"initial\" : undefined}\n        animate={animate ? \"animate\" : undefined}\n        transition={\n          animate\n            ? {\n                duration: 5,\n                repeat: Infinity,\n                repeatType: \"reverse\",\n              }\n            : undefined\n        }\n        style={{\n          backgroundSize: animate ? \"400% 400%\" : undefined,\n        }}\n        className={cn(\n          \"absolute inset-0 rounded-3xl z-[1] will-change-transform\",\n          \"bg-[radial-gradient(circle_farthest-side_at_0_100%,#0ea5e9,transparent),radial-gradient(circle_farthest-side_at_100%_0,#eab308,transparent),radial-gradient(circle_farthest-side_at_100%_100%,#0284c7,transparent),radial-gradient(circle_farthest-side_at_0_0,#38bdf8,#141316)]\"\n        )}\n      />\n\n      <div className={cn(\"relative z-10\", className)}>{children}</div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAMO,MAAM,qBAAqB,CAAC,EACjC,QAAQ,EACR,SAAS,EACT,kBAAkB,EAClB,UAAU,IAAI,EAMf;IACC,MAAM,WAAW;QACf,SAAS;YACP,oBAAoB;QACtB;QACA,SAAS;YACP,oBAAoB;gBAAC;gBAAU;gBAAY;aAAQ;QACrD;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;;0BAC3C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU,UAAU,WAAW;gBAC/B,SAAS,UAAU,YAAY;gBAC/B,SAAS,UAAU,YAAY;gBAC/B,YACE,UACI;oBACE,UAAU;oBACV,QAAQ;oBACR,YAAY;gBACd,IACA;gBAEN,OAAO;oBACL,gBAAgB,UAAU,cAAc;gBAC1C;gBACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+HACA;;;;;;0BAGJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU,UAAU,WAAW;gBAC/B,SAAS,UAAU,YAAY;gBAC/B,SAAS,UAAU,YAAY;gBAC/B,YACE,UACI;oBACE,UAAU;oBACV,QAAQ;oBACR,YAAY;gBACd,IACA;gBAEN,OAAO;oBACL,gBAAgB,UAAU,cAAc;gBAC1C;gBACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;;;;;;0BAIJ,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;0BAAa;;;;;;;;;;;;AAGvD", "debugId": null}}, {"offset": {"line": 895, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/sections/hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"motion/react\";\nimport { TextGenerateEffect } from \"@/components/ui/text-generate-effect\";\nimport { BackgroundGradient } from \"@/components/ui/background-gradient\";\nimport { HoverBorderGradient } from \"@/components/ui/hover-border-gradient\";\nimport { fadeInUp, staggerContainer } from \"@/lib/utils\";\nimport { ArrowRight, Star, Shield, Award } from \"lucide-react\";\n\nexport function Hero() {\n  const heroText = \"Automotive Artistry That Protects Your Investment\";\n  const subText = \"Premium detailing services with meticulous attention to every corner of your vehicle. From ceramic coating to paint correction, we deliver results that exceed expectations.\";\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-neutral-50 via-blue-50/30 to-yellow-50/20\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n      <div className=\"absolute top-20 left-10 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl animate-float\"></div>\n      <div className=\"absolute bottom-20 right-10 w-96 h-96 bg-yellow-400/20 rounded-full blur-3xl animate-float\" style={{ animationDelay: \"2s\" }}></div>\n      \n      <div className=\"container mx-auto px-4 relative z-10\">\n        <motion.div\n          variants={staggerContainer}\n          initial=\"initial\"\n          animate=\"animate\"\n          className=\"text-center max-w-6xl mx-auto\"\n        >\n          {/* Trust Indicators */}\n          <motion.div\n            variants={fadeInUp}\n            className=\"flex items-center justify-center gap-6 mb-8\"\n          >\n            <div className=\"flex items-center gap-2 text-sm text-neutral-600\">\n              <Star className=\"w-4 h-4 fill-yellow-400 text-yellow-400\" />\n              <span>5.0 Rating</span>\n            </div>\n            <div className=\"flex items-center gap-2 text-sm text-neutral-600\">\n              <Shield className=\"w-4 h-4 text-blue-600\" />\n              <span>Fully Insured</span>\n            </div>\n            <div className=\"flex items-center gap-2 text-sm text-neutral-600\">\n              <Award className=\"w-4 h-4 text-yellow-600\" />\n              <span>Certified Professional</span>\n            </div>\n          </motion.div>\n\n          {/* Main Heading */}\n          <motion.div variants={fadeInUp} className=\"mb-8\">\n            <TextGenerateEffect\n              words={heroText}\n              className=\"text-4xl md:text-6xl lg:text-7xl font-bold text-center bg-gradient-to-r from-neutral-800 via-blue-800 to-neutral-800 bg-clip-text text-transparent\"\n            />\n          </motion.div>\n\n          {/* Subheading */}\n          <motion.p\n            variants={fadeInUp}\n            className=\"text-lg md:text-xl lg:text-2xl text-neutral-600 max-w-4xl mx-auto mb-12 leading-relaxed\"\n          >\n            {subText}\n          </motion.p>\n\n          {/* CTA Buttons */}\n          <motion.div\n            variants={fadeInUp}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-16\"\n          >\n            <BackgroundGradient className=\"rounded-full\">\n              <button className=\"bg-white text-neutral-800 px-8 py-4 rounded-full font-semibold text-lg flex items-center gap-2 hover:shadow-lg transition-all duration-300\">\n                Get Free Quote\n                <ArrowRight className=\"w-5 h-5\" />\n              </button>\n            </BackgroundGradient>\n            \n            <HoverBorderGradient\n              containerClassName=\"rounded-full\"\n              className=\"bg-transparent text-neutral-800 px-8 py-4 font-semibold text-lg\"\n            >\n              View Our Work\n            </HoverBorderGradient>\n          </motion.div>\n\n          {/* Before/After Preview */}\n          <motion.div\n            variants={fadeInUp}\n            className=\"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto\"\n          >\n            <div className=\"relative group\">\n              <div className=\"absolute inset-0 bg-gradient-to-r from-red-500/20 to-orange-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300\"></div>\n              <div className=\"relative bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-xl\">\n                <div className=\"aspect-video bg-gradient-to-br from-red-100 to-orange-100 rounded-xl mb-4 flex items-center justify-center\">\n                  <span className=\"text-red-600 font-semibold\">BEFORE</span>\n                </div>\n                <h3 className=\"font-semibold text-neutral-800 mb-2\">Dull & Oxidized</h3>\n                <p className=\"text-sm text-neutral-600\">Faded paint, water spots, and surface contamination</p>\n              </div>\n            </div>\n\n            <div className=\"relative group\">\n              <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/20 to-green-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300\"></div>\n              <div className=\"relative bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-xl\">\n                <div className=\"aspect-video bg-gradient-to-br from-blue-100 to-green-100 rounded-xl mb-4 flex items-center justify-center\">\n                  <span className=\"text-blue-600 font-semibold\">AFTER</span>\n                </div>\n                <h3 className=\"font-semibold text-neutral-800 mb-2\">Showroom Shine</h3>\n                <p className=\"text-sm text-neutral-600\">Deep gloss, protected finish, and mirror-like clarity</p>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 2, duration: 1 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <div className=\"flex flex-col items-center gap-2 text-neutral-400\">\n          <span className=\"text-sm\">Scroll to explore</span>\n          <motion.div\n            animate={{ y: [0, 10, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-6 h-10 border-2 border-neutral-300 rounded-full flex justify-center\"\n          >\n            <motion.div\n              animate={{ y: [0, 12, 0] }}\n              transition={{ duration: 2, repeat: Infinity }}\n              className=\"w-1 h-3 bg-neutral-400 rounded-full mt-2\"\n            />\n          </motion.div>\n        </div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AASO,SAAS;IACd,MAAM,WAAW;IACjB,MAAM,UAAU;IAEhB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;gBAA6F,OAAO;oBAAE,gBAAgB;gBAAK;;;;;;0BAE1I,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU,mHAAA,CAAA,mBAAgB;oBAC1B,SAAQ;oBACR,SAAQ;oBACR,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU,mHAAA,CAAA,WAAQ;4BAClB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU,mHAAA,CAAA,WAAQ;4BAAE,WAAU;sCACxC,cAAA,8OAAC,sJAAA,CAAA,qBAAkB;gCACjB,OAAO;gCACP,WAAU;;;;;;;;;;;sCAKd,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU,mHAAA,CAAA,WAAQ;4BAClB,WAAU;sCAET;;;;;;sCAIH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU,mHAAA,CAAA,WAAQ;4BAClB,WAAU;;8CAEV,8OAAC,kJAAA,CAAA,qBAAkB;oCAAC,WAAU;8CAC5B,cAAA,8OAAC;wCAAO,WAAU;;4CAA6I;0DAE7J,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAI1B,8OAAC,uJAAA,CAAA,sBAAmB;oCAClB,oBAAmB;oCACnB,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU,mHAAA,CAAA,WAAQ;4BAClB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;8DAE/C,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;;;;;;;;;;;;;8CAI5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA8B;;;;;;;;;;;8DAEhD,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAG,UAAU;gBAAE;gBACpC,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;oCAAC;oCAAG;oCAAI;iCAAE;4BAAC;4BACzB,YAAY;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;4BAC5C,WAAU;sCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACzB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;gCAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB", "debugId": null}}, {"offset": {"line": 1319, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/ui/card-hover-effect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport { AnimatePresence, motion } from \"motion/react\";\nimport { useState } from \"react\";\n\nexport const HoverEffect = ({\n  items,\n  className,\n}: {\n  items: {\n    title: string;\n    description: string;\n    link?: string;\n    icon?: React.ReactNode;\n  }[];\n  className?: string;\n}) => {\n  let [hoveredIndex, setHoveredIndex] = useState<number | null>(null);\n\n  return (\n    <div\n      className={cn(\n        \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 py-10\",\n        className\n      )}\n    >\n      {items.map((item, idx) => (\n        <div\n          key={item?.link || idx}\n          className=\"relative group block p-2 h-full w-full\"\n          onMouseEnter={() => setHoveredIndex(idx)}\n          onMouseLeave={() => setHoveredIndex(null)}\n        >\n          <AnimatePresence>\n            {hoveredIndex === idx && (\n              <motion.span\n                className=\"absolute inset-0 h-full w-full bg-neutral-200 dark:bg-slate-800/[0.8] block rounded-3xl\"\n                layoutId=\"hoverBackground\"\n                initial={{ opacity: 0 }}\n                animate={{\n                  opacity: 1,\n                  transition: { duration: 0.15 },\n                }}\n                exit={{\n                  opacity: 0,\n                  transition: { duration: 0.15, delay: 0.2 },\n                }}\n              />\n            )}\n          </AnimatePresence>\n          <Card>\n            {item.icon && (\n              <div className=\"flex items-center justify-center w-12 h-12 mb-4 rounded-lg bg-gradient-to-br from-blue-500 to-yellow-500\">\n                {item.icon}\n              </div>\n            )}\n            <CardTitle>{item.title}</CardTitle>\n            <CardDescription>{item.description}</CardDescription>\n          </Card>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport const Card = ({\n  className,\n  children,\n}: {\n  className?: string;\n  children: React.ReactNode;\n}) => {\n  return (\n    <div\n      className={cn(\n        \"rounded-2xl h-full w-full p-4 overflow-hidden bg-white dark:bg-black border border-transparent dark:border-white/[0.2] group-hover:border-slate-700 relative z-20 shadow-lg\",\n        className\n      )}\n    >\n      <div className=\"relative z-50\">\n        <div className=\"p-4\">{children}</div>\n      </div>\n    </div>\n  );\n};\n\nexport const CardTitle = ({\n  className,\n  children,\n}: {\n  className?: string;\n  children: React.ReactNode;\n}) => {\n  return (\n    <h4 className={cn(\"text-zinc-800 dark:text-zinc-100 font-bold tracking-wide mt-4\", className)}>\n      {children}\n    </h4>\n  );\n};\n\nexport const CardDescription = ({\n  className,\n  children,\n}: {\n  className?: string;\n  children: React.ReactNode;\n}) => {\n  return (\n    <p\n      className={cn(\n        \"mt-8 text-zinc-600 dark:text-zinc-400 tracking-wide leading-relaxed text-sm\",\n        className\n      )}\n    >\n      {children}\n    </p>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMO,MAAM,cAAc,CAAC,EAC1B,KAAK,EACL,SAAS,EASV;IACC,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;kBAGD,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,8OAAC;gBAEC,WAAU;gBACV,cAAc,IAAM,gBAAgB;gBACpC,cAAc,IAAM,gBAAgB;;kCAEpC,8OAAC,yLAAA,CAAA,kBAAe;kCACb,iBAAiB,qBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,WAAU;4BACV,UAAS;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCACP,SAAS;gCACT,YAAY;oCAAE,UAAU;gCAAK;4BAC/B;4BACA,MAAM;gCACJ,SAAS;gCACT,YAAY;oCAAE,UAAU;oCAAM,OAAO;gCAAI;4BAC3C;;;;;;;;;;;kCAIN,8OAAC;;4BACE,KAAK,IAAI,kBACR,8OAAC;gCAAI,WAAU;0CACZ,KAAK,IAAI;;;;;;0CAGd,8OAAC;0CAAW,KAAK,KAAK;;;;;;0CACtB,8OAAC;0CAAiB,KAAK,WAAW;;;;;;;;;;;;;eA7B/B,MAAM,QAAQ;;;;;;;;;;AAmC7B;AAEO,MAAM,OAAO,CAAC,EACnB,SAAS,EACT,QAAQ,EAIT;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+KACA;kBAGF,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAO;;;;;;;;;;;;;;;;AAI9B;AAEO,MAAM,YAAY,CAAC,EACxB,SAAS,EACT,QAAQ,EAIT;IACC,qBACE,8OAAC;QAAG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iEAAiE;kBAChF;;;;;;AAGP;AAEO,MAAM,kBAAkB,CAAC,EAC9B,SAAS,EACT,QAAQ,EAIT;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;kBAGD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1466, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/sections/services.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"motion/react\";\nimport { HoverEffect } from \"@/components/ui/card-hover-effect\";\nimport { scrollFadeIn } from \"@/lib/utils\";\nimport { \n  Sparkles, \n  Shield, \n  Truck, \n  Paintbrush, \n  Droplets, \n  Car \n} from \"lucide-react\";\n\nexport function Services() {\n  const services = [\n    {\n      title: \"Ceramic Coating\",\n      description: \"Advanced nano-ceramic protection that bonds to your paint, providing years of protection against UV rays, chemicals, and environmental contaminants.\",\n      icon: <Shield className=\"w-6 h-6 text-white\" />,\n    },\n    {\n      title: \"Paint Correction\",\n      description: \"Multi-stage polishing process to remove swirl marks, scratches, and oxidation, restoring your vehicle's paint to better-than-new condition.\",\n      icon: <Paintbrush className=\"w-6 h-6 text-white\" />,\n    },\n    {\n      title: \"Mobile Detailing\",\n      description: \"Professional detailing services brought directly to your location. Convenience without compromising on quality or attention to detail.\",\n      icon: <Truck className=\"w-6 h-6 text-white\" />,\n    },\n    {\n      title: \"Interior Deep Clean\",\n      description: \"Comprehensive interior restoration including leather conditioning, fabric protection, and steam cleaning for a fresh, pristine cabin.\",\n      icon: <Sparkles className=\"w-6 h-6 text-white\" />,\n    },\n    {\n      title: \"Hydrophobic Treatment\",\n      description: \"Advanced water-repelling technology that makes maintenance easier while providing superior protection against water damage and staining.\",\n      icon: <Droplets className=\"w-6 h-6 text-white\" />,\n    },\n    {\n      title: \"Full Vehicle Restoration\",\n      description: \"Complete transformation service combining all our expertise to bring neglected vehicles back to showroom condition and beyond.\",\n      icon: <Car className=\"w-6 h-6 text-white\" />,\n    },\n  ];\n\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-neutral-50 to-white relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-yellow-500 to-blue-500\"></div>\n      \n      <div className=\"container mx-auto px-4\">\n        <motion.div\n          {...scrollFadeIn}\n          className=\"text-center mb-16\"\n        >\n          <motion.span\n            {...scrollFadeIn}\n            className=\"inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-4\"\n          >\n            Our Services\n          </motion.span>\n          \n          <motion.h2\n            {...scrollFadeIn}\n            className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-800 mb-6\"\n          >\n            Four Corners of\n            <span className=\"bg-gradient-to-r from-blue-600 to-yellow-600 bg-clip-text text-transparent\"> Excellence</span>\n          </motion.h2>\n          \n          <motion.p\n            {...scrollFadeIn}\n            className=\"text-lg md:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed\"\n          >\n            Every vehicle receives our comprehensive four-corner approach: meticulous preparation, \n            expert application, quality assurance, and ongoing protection. No detail overlooked, \n            no corner cut.\n          </motion.p>\n        </motion.div>\n\n        <motion.div\n          {...scrollFadeIn}\n          className=\"mb-16\"\n        >\n          <HoverEffect items={services} className=\"max-w-6xl mx-auto\" />\n        </motion.div>\n\n        {/* Process Overview */}\n        <motion.div\n          {...scrollFadeIn}\n          className=\"bg-gradient-to-r from-blue-50 to-yellow-50 rounded-3xl p-8 md:p-12 max-w-4xl mx-auto\"\n        >\n          <h3 className=\"text-2xl md:text-3xl font-bold text-center text-neutral-800 mb-8\">\n            Our Four-Corner Process\n          </h3>\n          \n          <div className=\"grid md:grid-cols-4 gap-6\">\n            {[\n              { number: \"01\", title: \"Assessment\", description: \"Thorough inspection and consultation\" },\n              { number: \"02\", title: \"Preparation\", description: \"Meticulous cleaning and surface prep\" },\n              { number: \"03\", title: \"Application\", description: \"Expert technique and premium products\" },\n              { number: \"04\", title: \"Protection\", description: \"Final inspection and care instructions\" },\n            ].map((step, index) => (\n              <motion.div\n                key={index}\n                {...scrollFadeIn}\n                transition={{ delay: index * 0.1 }}\n                className=\"text-center\"\n              >\n                <div className=\"w-16 h-16 bg-gradient-to-r from-blue-500 to-yellow-500 rounded-full flex items-center justify-center text-white font-bold text-lg mx-auto mb-4\">\n                  {step.number}\n                </div>\n                <h4 className=\"font-semibold text-neutral-800 mb-2\">{step.title}</h4>\n                <p className=\"text-sm text-neutral-600\">{step.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAcO,SAAS;IACd,MAAM,WAAW;QACf;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC1B;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,8MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC9B;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;QACvB;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACR,GAAG,mHAAA,CAAA,eAAY;wBAChB,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gCACT,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;0CACX;;;;;;0CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACP,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;;oCACX;kDAEC,8OAAC;wCAAK,WAAU;kDAA6E;;;;;;;;;;;;0CAG/F,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACN,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;0CACX;;;;;;;;;;;;kCAOH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACR,GAAG,mHAAA,CAAA,eAAY;wBAChB,WAAU;kCAEV,cAAA,8OAAC,mJAAA,CAAA,cAAW;4BAAC,OAAO;4BAAU,WAAU;;;;;;;;;;;kCAI1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACR,GAAG,mHAAA,CAAA,eAAY;wBAChB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAmE;;;;;;0CAIjF,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,QAAQ;wCAAM,OAAO;wCAAc,aAAa;oCAAuC;oCACzF;wCAAE,QAAQ;wCAAM,OAAO;wCAAe,aAAa;oCAAuC;oCAC1F;wCAAE,QAAQ;wCAAM,OAAO;wCAAe,aAAa;oCAAwC;oCAC3F;wCAAE,QAAQ;wCAAM,OAAO;wCAAc,aAAa;oCAAyC;iCAC5F,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAER,GAAG,mHAAA,CAAA,eAAY;wCAChB,YAAY;4CAAE,OAAO,QAAQ;wCAAI;wCACjC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACZ,KAAK,MAAM;;;;;;0DAEd,8OAAC;gDAAG,WAAU;0DAAuC,KAAK,KAAK;;;;;;0DAC/D,8OAAC;gDAAE,WAAU;0DAA4B,KAAK,WAAW;;;;;;;uCATpD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBrB", "debugId": null}}]}