{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Animation utilities\nexport const fadeInUp = {\n  initial: { opacity: 0, y: 60 },\n  animate: { opacity: 1, y: 0 },\n  transition: { duration: 0.6, ease: [0.6, -0.05, 0.01, 0.99] as const }\n}\n\nexport const fadeInLeft = {\n  initial: { opacity: 0, x: -60 },\n  animate: { opacity: 1, x: 0 },\n  transition: { duration: 0.6, ease: [0.6, -0.05, 0.01, 0.99] as const }\n}\n\nexport const fadeInRight = {\n  initial: { opacity: 0, x: 60 },\n  animate: { opacity: 1, x: 0 },\n  transition: { duration: 0.6, ease: [0.6, -0.05, 0.01, 0.99] as const }\n}\n\nexport const staggerContainer = {\n  initial: {},\n  animate: {\n    transition: {\n      staggerChildren: 0.1,\n      delayChildren: 0.3\n    }\n  }\n}\n\nexport const scaleIn = {\n  initial: { opacity: 0, scale: 0.8 },\n  animate: { opacity: 1, scale: 1 },\n  transition: { duration: 0.5, ease: [0.6, -0.05, 0.01, 0.99] as const }\n}\n\n// Scroll animation utilities\nexport const scrollFadeIn = {\n  initial: { opacity: 0, y: 50 },\n  whileInView: { opacity: 1, y: 0 },\n  transition: { duration: 0.8, ease: [0.6, -0.05, 0.01, 0.99] as const },\n  viewport: { once: true, margin: \"-100px\" }\n}\n\n// Create a function to generate scroll animations with custom delays\nexport const createScrollFadeIn = (delay: number = 0) => ({\n  initial: { opacity: 0, y: 50 },\n  whileInView: { opacity: 1, y: 0 },\n  transition: { duration: 0.8, ease: [0.6, -0.05, 0.01, 0.99] as const, delay },\n  viewport: { once: true, margin: \"-100px\" }\n})\n\n// Luxury brand color palette\nexport const colors = {\n  primary: {\n    50: '#f0f9ff',\n    100: '#e0f2fe',\n    200: '#bae6fd',\n    300: '#7dd3fc',\n    400: '#38bdf8',\n    500: '#0ea5e9',\n    600: '#0284c7',\n    700: '#0369a1',\n    800: '#075985',\n    900: '#0c4a6e',\n  },\n  accent: {\n    50: '#fefce8',\n    100: '#fef9c3',\n    200: '#fef08a',\n    300: '#fde047',\n    400: '#facc15',\n    500: '#eab308',\n    600: '#ca8a04',\n    700: '#a16207',\n    800: '#854d0e',\n    900: '#713f12',\n  },\n  neutral: {\n    50: '#fafafa',\n    100: '#f5f5f5',\n    200: '#e5e5e5',\n    300: '#d4d4d4',\n    400: '#a3a3a3',\n    500: '#737373',\n    600: '#525252',\n    700: '#404040',\n    800: '#262626',\n    900: '#171717',\n  }\n}\n\n// Typography utilities\nexport const typography = {\n  heading: {\n    h1: \"text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight\",\n    h2: \"text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight\",\n    h3: \"text-2xl md:text-3xl lg:text-4xl font-semibold tracking-tight\",\n    h4: \"text-xl md:text-2xl lg:text-3xl font-semibold tracking-tight\",\n  },\n  body: {\n    large: \"text-lg md:text-xl leading-relaxed\",\n    base: \"text-base md:text-lg leading-relaxed\",\n    small: \"text-sm md:text-base leading-relaxed\",\n  }\n}\n\n// Responsive breakpoints\nexport const breakpoints = {\n  sm: '640px',\n  md: '768px',\n  lg: '1024px',\n  xl: '1280px',\n  '2xl': '1536px',\n}\n\n// Luxury animation presets\nexport const luxuryAnimations = {\n  gentleFloat: {\n    animate: {\n      y: [-10, 10, -10],\n      transition: {\n        duration: 6,\n        repeat: Infinity,\n        ease: [0.4, 0, 0.6, 1]\n      }\n    }\n  },\n  shimmer: {\n    animate: {\n      backgroundPosition: [\"0% 50%\", \"100% 50%\", \"0% 50%\"],\n      transition: {\n        duration: 3,\n        repeat: Infinity,\n        ease: [0, 0, 1, 1]\n      }\n    }\n  },\n  luxuryHover: {\n    whileHover: {\n      scale: 1.05,\n      transition: { duration: 0.3, ease: [0.6, -0.05, 0.01, 0.99] }\n    },\n    whileTap: {\n      scale: 0.95,\n      transition: { duration: 0.1 }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,WAAW;IACtB,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,YAAY;QAAE,UAAU;QAAK,MAAM;YAAC;YAAK,CAAC;YAAM;YAAM;SAAK;IAAU;AACvE;AAEO,MAAM,aAAa;IACxB,SAAS;QAAE,SAAS;QAAG,GAAG,CAAC;IAAG;IAC9B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,YAAY;QAAE,UAAU;QAAK,MAAM;YAAC;YAAK,CAAC;YAAM;YAAM;SAAK;IAAU;AACvE;AAEO,MAAM,cAAc;IACzB,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,YAAY;QAAE,UAAU;QAAK,MAAM;YAAC;YAAK,CAAC;YAAM;YAAM;SAAK;IAAU;AACvE;AAEO,MAAM,mBAAmB;IAC9B,SAAS,CAAC;IACV,SAAS;QACP,YAAY;YACV,iBAAiB;YACjB,eAAe;QACjB;IACF;AACF;AAEO,MAAM,UAAU;IACrB,SAAS;QAAE,SAAS;QAAG,OAAO;IAAI;IAClC,SAAS;QAAE,SAAS;QAAG,OAAO;IAAE;IAChC,YAAY;QAAE,UAAU;QAAK,MAAM;YAAC;YAAK,CAAC;YAAM;YAAM;SAAK;IAAU;AACvE;AAGO,MAAM,eAAe;IAC1B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,aAAa;QAAE,SAAS;QAAG,GAAG;IAAE;IAChC,YAAY;QAAE,UAAU;QAAK,MAAM;YAAC;YAAK,CAAC;YAAM;YAAM;SAAK;IAAU;IACrE,UAAU;QAAE,MAAM;QAAM,QAAQ;IAAS;AAC3C;AAGO,MAAM,qBAAqB,CAAC,QAAgB,CAAC,GAAK,CAAC;QACxD,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,MAAM;gBAAC;gBAAK,CAAC;gBAAM;gBAAM;aAAK;YAAW;QAAM;QAC5E,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAS;IAC3C,CAAC;AAGM,MAAM,SAAS;IACpB,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,QAAQ;QACN,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAGO,MAAM,aAAa;IACxB,SAAS;QACP,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IACA,MAAM;QACJ,OAAO;QACP,MAAM;QACN,OAAO;IACT;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT;AAGO,MAAM,mBAAmB;IAC9B,aAAa;QACX,SAAS;YACP,GAAG;gBAAC,CAAC;gBAAI;gBAAI,CAAC;aAAG;YACjB,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;oBAAC;oBAAK;oBAAG;oBAAK;iBAAE;YACxB;QACF;IACF;IACA,SAAS;QACP,SAAS;YACP,oBAAoB;gBAAC;gBAAU;gBAAY;aAAS;YACpD,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;oBAAC;oBAAG;oBAAG;oBAAG;iBAAE;YACpB;QACF;IACF;IACA,aAAa;QACX,YAAY;YACV,OAAO;YACP,YAAY;gBAAE,UAAU;gBAAK,MAAM;oBAAC;oBAAK,CAAC;oBAAM;oBAAM;iBAAK;YAAC;QAC9D;QACA,UAAU;YACR,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;AACF", "debugId": null}}, {"offset": {"line": 292, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/ui/hover-border-gradient.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { motion } from \"motion/react\";\nimport { cn } from \"@/lib/utils\";\n\ntype Direction = \"TOP\" | \"LEFT\" | \"BOTTOM\" | \"RIGHT\";\n\nexport function HoverBorderGradient({\n  children,\n  containerClassName,\n  className,\n  as: Tag = \"button\",\n  duration = 1,\n  clockwise = true,\n  ...props\n}: React.PropsWithChildren<\n  {\n    as?: React.ElementType;\n    containerClassName?: string;\n    className?: string;\n    duration?: number;\n    clockwise?: boolean;\n  } & React.HTMLAttributes<HTMLElement>\n>) {\n  const [hovered, setHovered] = useState<boolean>(false);\n  const [direction, setDirection] = useState<Direction>(\"TOP\");\n\n  const rotateDirection = (currentDirection: Direction): Direction => {\n    const directions: Direction[] = [\"TOP\", \"LEFT\", \"BOTTOM\", \"RIGHT\"];\n    const currentIndex = directions.indexOf(currentDirection);\n    const nextIndex = clockwise\n      ? (currentIndex - 1 + directions.length) % directions.length\n      : (currentIndex + 1) % directions.length;\n    return directions[nextIndex];\n  };\n\n  const movingMap: Record<Direction, string> = {\n    TOP: \"radial-gradient(20.7% 50% at 50% 0%, hsl(0, 0%, 100%) 0%, rgba(255, 255, 255, 0) 100%)\",\n    LEFT: \"radial-gradient(16.6% 43.1% at 0% 50%, hsl(0, 0%, 100%) 0%, rgba(255, 255, 255, 0) 100%)\",\n    BOTTOM:\n      \"radial-gradient(20.7% 50% at 50% 100%, hsl(0, 0%, 100%) 0%, rgba(255, 255, 255, 0) 100%)\",\n    RIGHT:\n      \"radial-gradient(16.2% 41.199999999999996% at 100% 50%, hsl(0, 0%, 100%) 0%, rgba(255, 255, 255, 0) 100%)\",\n  };\n\n  const highlight =\n    \"radial-gradient(75% 181.15942028985506% at 50% 50%, #0ea5e9 0%, rgba(255, 255, 255, 0) 100%)\";\n\n  useEffect(() => {\n    if (!hovered) {\n      const interval = setInterval(() => {\n        setDirection((prevState) => rotateDirection(prevState));\n      }, duration * 1000);\n      return () => clearInterval(interval);\n    }\n  }, [hovered]);\n  \n  return (\n    <Tag\n      onMouseEnter={(event: React.MouseEvent<HTMLDivElement>) => {\n        setHovered(true);\n      }}\n      onMouseLeave={() => setHovered(false)}\n      className={cn(\n        \"relative flex rounded-full border content-center bg-black/20 hover:bg-black/10 transition duration-500 dark:bg-white/20 items-center flex-col flex-nowrap gap-10 h-min justify-center overflow-visible p-px decoration-clone w-fit\",\n        containerClassName\n      )}\n      {...props}\n    >\n      <div\n        className={cn(\n          \"w-auto text-white z-10 bg-black px-4 py-2 rounded-[inherit]\",\n          className\n        )}\n      >\n        {children}\n      </div>\n      <motion.div\n        className={cn(\n          \"flex-none inset-0 overflow-hidden absolute z-0 rounded-[inherit]\"\n        )}\n        style={{\n          filter: \"blur(2px)\",\n          position: \"absolute\",\n          width: \"100%\",\n          height: \"100%\",\n        }}\n        initial={{ background: movingMap[direction] }}\n        animate={{\n          background: hovered\n            ? [movingMap[direction], highlight]\n            : movingMap[direction],\n        }}\n        transition={{ ease: \"linear\", duration: duration ?? 1 }}\n      />\n      <div className=\"bg-black absolute z-1 flex-none inset-[2px] rounded-[100px]\" />\n    </Tag>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAQO,SAAS,oBAAoB,EAClC,QAAQ,EACR,kBAAkB,EAClB,SAAS,EACT,IAAI,MAAM,QAAQ,EAClB,WAAW,CAAC,EACZ,YAAY,IAAI,EAChB,GAAG,OASJ;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IAEtD,MAAM,kBAAkB,CAAC;QACvB,MAAM,aAA0B;YAAC;YAAO;YAAQ;YAAU;SAAQ;QAClE,MAAM,eAAe,WAAW,OAAO,CAAC;QACxC,MAAM,YAAY,YACd,CAAC,eAAe,IAAI,WAAW,MAAM,IAAI,WAAW,MAAM,GAC1D,CAAC,eAAe,CAAC,IAAI,WAAW,MAAM;QAC1C,OAAO,UAAU,CAAC,UAAU;IAC9B;IAEA,MAAM,YAAuC;QAC3C,KAAK;QACL,MAAM;QACN,QACE;QACF,OACE;IACJ;IAEA,MAAM,YACJ;IAEF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS;YACZ,MAAM,WAAW,YAAY;gBAC3B,aAAa,CAAC,YAAc,gBAAgB;YAC9C,GAAG,WAAW;YACd,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;KAAQ;IAEZ,qBACE,8OAAC;QACC,cAAc,CAAC;YACb,WAAW;QACb;QACA,cAAc,IAAM,WAAW;QAC/B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;0BAGD;;;;;;0BAEH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;gBAEF,OAAO;oBACL,QAAQ;oBACR,UAAU;oBACV,OAAO;oBACP,QAAQ;gBACV;gBACA,SAAS;oBAAE,YAAY,SAAS,CAAC,UAAU;gBAAC;gBAC5C,SAAS;oBACP,YAAY,UACR;wBAAC,SAAS,CAAC,UAAU;wBAAE;qBAAU,GACjC,SAAS,CAAC,UAAU;gBAC1B;gBACA,YAAY;oBAAE,MAAM;oBAAU,UAAU,YAAY;gBAAE;;;;;;0BAExD,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/navigation/navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"motion/react\";\nimport { Menu, X, Phone, MapPin } from \"lucide-react\";\nimport { HoverBorderGradient } from \"@/components/ui/hover-border-gradient\";\n\nexport function Navbar() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  const navItems = [\n    { name: \"Services\", href: \"#services\" },\n    { name: \"Process\", href: \"#process\" },\n    { name: \"Gallery\", href: \"#gallery\" },\n    { name: \"About\", href: \"#about\" },\n    { name: \"Contact\", href: \"#contact\" },\n  ];\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.6, ease: \"easeOut\" }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${\n        scrolled\n          ? \"backdrop-blur-luxury bg-white/80 luxury-shadow border-b border-white/30\"\n          : \"bg-transparent\"\n      }`}\n    >\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16 md:h-20\">\n          {/* Logo */}\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"flex items-center space-x-3 magnetic-hover\"\n          >\n            <div className=\"w-12 h-12 luxury-gradient rounded-xl flex items-center justify-center luxury-shadow\">\n              <span className=\"text-white font-bold text-xl\">J</span>\n            </div>\n            <div className=\"hidden sm:block\">\n              <h1 className=\"text-xl font-bold text-neutral-800\">\n                Jon Four Corners\n              </h1>\n              <p className=\"text-xs text-neutral-600 -mt-1 font-medium\">Detailing</p>\n            </div>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <motion.a\n                key={item.name}\n                href={item.href}\n                whileHover={{ y: -2 }}\n                className=\"text-neutral-700 hover:text-blue-600 transition-colors duration-200 font-medium\"\n              >\n                {item.name}\n              </motion.a>\n            ))}\n          </div>\n\n          {/* Contact Info & CTA */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-4 text-sm text-neutral-600\">\n              <div className=\"flex items-center space-x-1\">\n                <Phone className=\"w-4 h-4\" />\n                <span>(*************</span>\n              </div>\n              <div className=\"flex items-center space-x-1\">\n                <MapPin className=\"w-4 h-4\" />\n                <span>Four Corners Area</span>\n              </div>\n            </div>\n            \n            <HoverBorderGradient\n              containerClassName=\"rounded-full\"\n              className=\"bg-transparent text-neutral-800 px-6 py-2 text-sm font-semibold\"\n            >\n              Get Quote\n            </HoverBorderGradient>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <motion.button\n            whileTap={{ scale: 0.95 }}\n            onClick={() => setIsOpen(!isOpen)}\n            className=\"md:hidden p-2 rounded-lg bg-neutral-100 text-neutral-700\"\n          >\n            {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n          </motion.button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"md:hidden bg-white/95 backdrop-blur-md border-t border-neutral-200/50\"\n          >\n            <div className=\"container mx-auto px-4 py-4\">\n              <div className=\"flex flex-col space-y-4\">\n                {navItems.map((item, index) => (\n                  <motion.a\n                    key={item.name}\n                    href={item.href}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    onClick={() => setIsOpen(false)}\n                    className=\"text-neutral-700 hover:text-blue-600 transition-colors duration-200 font-medium py-2\"\n                  >\n                    {item.name}\n                  </motion.a>\n                ))}\n                \n                <div className=\"pt-4 border-t border-neutral-200\">\n                  <div className=\"flex flex-col space-y-2 text-sm text-neutral-600 mb-4\">\n                    <div className=\"flex items-center space-x-2\">\n                      <Phone className=\"w-4 h-4\" />\n                      <span>(*************</span>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <MapPin className=\"w-4 h-4\" />\n                      <span>Four Corners Area</span>\n                    </div>\n                  </div>\n                  \n                  <HoverBorderGradient\n                    containerClassName=\"rounded-full w-full\"\n                    className=\"bg-transparent text-neutral-800 px-6 py-3 text-sm font-semibold w-full text-center\"\n                  >\n                    Get Free Quote\n                  </HoverBorderGradient>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAOO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,WAAW,CAAC,4DAA4D,EACtE,WACI,4EACA,kBACJ;;0BAEF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDAGnD,8OAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;sCAK9D,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,WAAU;8CAET,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;;;;;;sCAWpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAIV,8OAAC,uJAAA,CAAA,sBAAmB;oCAClB,oBAAmB;oCACnB,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,UAAU;gCAAE,OAAO;4BAAK;4BACxB,SAAS,IAAM,UAAU,CAAC;4BAC1B,WAAU;sCAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM5D,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,KAAK,IAAI;wCACf,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,QAAQ;wCAAI;wCACjC,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAET,KAAK,IAAI;uCARL,KAAK,IAAI;;;;;8CAYlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;sDAIV,8OAAC,uJAAA,CAAA,sBAAmB;4CAClB,oBAAmB;4CACnB,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/navigation/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"motion/react\";\nimport { \n  Phone, \n  Mail, \n  MapPin, \n  Facebook, \n  Instagram, \n  Twitter,\n  Star,\n  Shield,\n  Award\n} from \"lucide-react\";\n\nexport function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  const quickLinks = [\n    { name: \"Services\", href: \"#services\" },\n    { name: \"Gallery\", href: \"#gallery\" },\n    { name: \"About\", href: \"#about\" },\n    { name: \"Contact\", href: \"#contact\" },\n    { name: \"Reviews\", href: \"#testimonials\" },\n  ];\n\n  const services = [\n    \"Ceramic Coating\",\n    \"Paint Correction\",\n    \"Interior Detailing\",\n    \"Mobile Service\",\n    \"Vehicle Restoration\",\n  ];\n\n  const socialLinks = [\n    { icon: <Facebook className=\"w-5 h-5\" />, href: \"#\", name: \"Facebook\" },\n    { icon: <Instagram className=\"w-5 h-5\" />, href: \"#\", name: \"Instagram\" },\n    { icon: <Twitter className=\"w-5 h-5\" />, href: \"#\", name: \"Twitter\" },\n  ];\n\n  return (\n    <footer className=\"bg-gradient-to-b from-neutral-900 to-black text-white relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-yellow-500 to-blue-500\"></div>\n      <div className=\"absolute top-10 right-10 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl\"></div>\n      <div className=\"absolute bottom-10 left-10 w-64 h-64 bg-yellow-500/10 rounded-full blur-3xl\"></div>\n\n      <div className=\"container mx-auto px-4 pt-16 pb-8 relative z-10\">\n        {/* Main Footer Content */}\n        <div className=\"grid lg:grid-cols-4 md:grid-cols-2 gap-8 mb-12\">\n          {/* Company Info */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"lg:col-span-1\"\n          >\n            <div className=\"flex items-center space-x-3 mb-6\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-blue-600 to-yellow-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-xl\">J</span>\n              </div>\n              <div>\n                <h3 className=\"text-xl font-bold\">Jon Four Corners</h3>\n                <p className=\"text-sm text-neutral-400\">Detailing</p>\n              </div>\n            </div>\n            \n            <p className=\"text-neutral-300 leading-relaxed mb-6\">\n              Premium automotive detailing services with artisan-level craftsmanship. \n              Protecting your investment with meticulous attention to every corner.\n            </p>\n\n            {/* Trust Badges */}\n            <div className=\"flex items-center gap-4 text-sm text-neutral-400\">\n              <div className=\"flex items-center gap-1\">\n                <Star className=\"w-4 h-4 fill-yellow-400 text-yellow-400\" />\n                <span>5.0 Rating</span>\n              </div>\n              <div className=\"flex items-center gap-1\">\n                <Shield className=\"w-4 h-4 text-blue-400\" />\n                <span>Insured</span>\n              </div>\n              <div className=\"flex items-center gap-1\">\n                <Award className=\"w-4 h-4 text-yellow-400\" />\n                <span>Certified</span>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Quick Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.1 }}\n            viewport={{ once: true }}\n          >\n            <h4 className=\"text-lg font-semibold mb-6\">Quick Links</h4>\n            <ul className=\"space-y-3\">\n              {quickLinks.map((link) => (\n                <li key={link.name}>\n                  <motion.a\n                    href={link.href}\n                    whileHover={{ x: 5 }}\n                    className=\"text-neutral-300 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </motion.a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Services */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            <h4 className=\"text-lg font-semibold mb-6\">Our Services</h4>\n            <ul className=\"space-y-3\">\n              {services.map((service) => (\n                <li key={service}>\n                  <span className=\"text-neutral-300\">{service}</span>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Contact Info */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.3 }}\n            viewport={{ once: true }}\n          >\n            <h4 className=\"text-lg font-semibold mb-6\">Get In Touch</h4>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center gap-3\">\n                <Phone className=\"w-5 h-5 text-blue-400\" />\n                <div>\n                  <p className=\"text-white font-medium\">(*************</p>\n                  <p className=\"text-sm text-neutral-400\">Call or text anytime</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center gap-3\">\n                <Mail className=\"w-5 h-5 text-blue-400\" />\n                <div>\n                  <p className=\"text-white font-medium\"><EMAIL></p>\n                  <p className=\"text-sm text-neutral-400\">Quick response guaranteed</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center gap-3\">\n                <MapPin className=\"w-5 h-5 text-blue-400\" />\n                <div>\n                  <p className=\"text-white font-medium\">Four Corners Region</p>\n                  <p className=\"text-sm text-neutral-400\">Mobile service available</p>\n                </div>\n              </div>\n            </div>\n\n            {/* Social Links */}\n            <div className=\"mt-6\">\n              <h5 className=\"text-sm font-semibold mb-3\">Follow Us</h5>\n              <div className=\"flex gap-3\">\n                {socialLinks.map((social) => (\n                  <motion.a\n                    key={social.name}\n                    href={social.href}\n                    whileHover={{ scale: 1.1, y: -2 }}\n                    whileTap={{ scale: 0.95 }}\n                    className=\"w-10 h-10 bg-neutral-800 rounded-lg flex items-center justify-center text-neutral-400 hover:text-white hover:bg-gradient-to-r hover:from-blue-600 hover:to-yellow-600 transition-all duration-300\"\n                  >\n                    {social.icon}\n                  </motion.a>\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Bottom Bar */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"border-t border-neutral-800 pt-8\"\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center gap-4\">\n            <div className=\"text-sm text-neutral-400\">\n              © {currentYear} Jon Four Corners Detailing. All rights reserved.\n            </div>\n            \n            <div className=\"flex gap-6 text-sm text-neutral-400\">\n              <motion.a\n                href=\"#\"\n                whileHover={{ color: \"#ffffff\" }}\n                className=\"hover:text-white transition-colors duration-200\"\n              >\n                Privacy Policy\n              </motion.a>\n              <motion.a\n                href=\"#\"\n                whileHover={{ color: \"#ffffff\" }}\n                className=\"hover:text-white transition-colors duration-200\"\n              >\n                Terms of Service\n              </motion.a>\n              <motion.a\n                href=\"#\"\n                whileHover={{ color: \"#ffffff\" }}\n                className=\"hover:text-white transition-colors duration-200\"\n              >\n                Warranty\n              </motion.a>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Call to Action */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-8 pt-8 border-t border-neutral-800\"\n        >\n          <p className=\"text-neutral-300 mb-4\">\n            Ready to give your vehicle the premium care it deserves?\n          </p>\n          <motion.button\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            className=\"bg-gradient-to-r from-blue-600 to-yellow-600 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300\"\n          >\n            Get Your Free Quote Today\n          </motion.button>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAeO,SAAS;IACd,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,aAAa;QACjB;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAW,MAAM;QAAgB;KAC1C;IAED,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,cAAc;QAClB;YAAE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAAc,MAAM;YAAK,MAAM;QAAW;QACtE;YAAE,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAAc,MAAM;YAAK,MAAM;QAAY;QACxE;YAAE,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAAc,MAAM;YAAK,MAAM;QAAU;KACrE;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoB;;;;;;kEAClC,8OAAC;wDAAE,WAAU;kEAA2B;;;;;;;;;;;;;;;;;;kDAI5C,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAMrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAMZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;0DACC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDACP,MAAM,KAAK,IAAI;oDACf,YAAY;wDAAE,GAAG;oDAAE;oDACnB,WAAU;8DAET,KAAK,IAAI;;;;;;+CANL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAcxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;0DACC,cAAA,8OAAC;oDAAK,WAAU;8DAAoB;;;;;;+CAD7B;;;;;;;;;;;;;;;;0CAQf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAyB;;;;;;0EACtC,8OAAC;gEAAE,WAAU;0EAA2B;;;;;;;;;;;;;;;;;;0DAI5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAyB;;;;;;0EACtC,8OAAC;gEAAE,WAAU;0EAA2B;;;;;;;;;;;;;;;;;;0DAI5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAyB;;;;;;0EACtC,8OAAC;gEAAE,WAAU;0EAA2B;;;;;;;;;;;;;;;;;;;;;;;;kDAM9C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wDAEP,MAAM,OAAO,IAAI;wDACjB,YAAY;4DAAE,OAAO;4DAAK,GAAG,CAAC;wDAAE;wDAChC,UAAU;4DAAE,OAAO;wDAAK;wDACxB,WAAU;kEAET,OAAO,IAAI;uDANP,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAe5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,aAAa;4BAAE,SAAS;wBAAE;wBAC1B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCAA2B;wCACrC;wCAAY;;;;;;;8CAGjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,MAAK;4CACL,YAAY;gDAAE,OAAO;4CAAU;4CAC/B,WAAU;sDACX;;;;;;sDAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,MAAK;4CACL,YAAY;gDAAE,OAAO;4CAAU;4CAC/B,WAAU;sDACX;;;;;;sDAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,MAAK;4CACL,YAAY;gDAAE,OAAO;4CAAU;4CAC/B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAGrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 1531, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/ui/text-generate-effect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { motion, stagger, useAnimate } from \"motion/react\";\nimport { cn } from \"@/lib/utils\";\n\nexport const TextGenerateEffect = ({\n  words,\n  className,\n  filter = true,\n  duration = 0.5,\n}: {\n  words: string;\n  className?: string;\n  filter?: boolean;\n  duration?: number;\n}) => {\n  const [scope, animate] = useAnimate();\n  let wordsArray = words.split(\" \");\n  \n  useEffect(() => {\n    animate(\n      \"span\",\n      {\n        opacity: 1,\n        filter: filter ? \"blur(0px)\" : \"none\",\n      },\n      {\n        duration: duration ? duration : 1,\n        delay: stagger(0.2),\n      }\n    );\n  }, [scope.current]);\n\n  const renderWords = () => {\n    return (\n      <motion.div ref={scope}>\n        {wordsArray.map((word, idx) => {\n          return (\n            <motion.span\n              key={word + idx}\n              className=\"dark:text-white text-black opacity-0\"\n              style={{\n                filter: filter ? \"blur(10px)\" : \"none\",\n              }}\n            >\n              {word}{\" \"}\n            </motion.span>\n          );\n        })}\n      </motion.div>\n    );\n  };\n\n  return (\n    <div className={cn(\"font-bold\", className)}>\n      <div className=\"mt-4\">\n        <div className=\"dark:text-white text-black text-2xl leading-snug tracking-wide\">\n          {renderWords()}\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAMO,MAAM,qBAAqB,CAAC,EACjC,KAAK,EACL,SAAS,EACT,SAAS,IAAI,EACb,WAAW,GAAG,EAMf;IACC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAA,GAAA,uLAAA,CAAA,aAAU,AAAD;IAClC,IAAI,aAAa,MAAM,KAAK,CAAC;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QACE,QACA;YACE,SAAS;YACT,QAAQ,SAAS,cAAc;QACjC,GACA;YACE,UAAU,WAAW,WAAW;YAChC,OAAO,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE;QACjB;IAEJ,GAAG;QAAC,MAAM,OAAO;KAAC;IAElB,MAAM,cAAc;QAClB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAAC,KAAK;sBACd,WAAW,GAAG,CAAC,CAAC,MAAM;gBACrB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBAEV,WAAU;oBACV,OAAO;wBACL,QAAQ,SAAS,eAAe;oBAClC;;wBAEC;wBAAM;;mBANF,OAAO;;;;;YASlB;;;;;;IAGN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC9B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 1613, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/ui/background-gradient.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport React from \"react\";\nimport { motion } from \"motion/react\";\n\nexport const BackgroundGradient = ({\n  children,\n  className,\n  containerClassName,\n  animate = true,\n}: {\n  children?: React.ReactNode;\n  className?: string;\n  containerClassName?: string;\n  animate?: boolean;\n}) => {\n  const variants = {\n    initial: {\n      backgroundPosition: \"0 50%\",\n    },\n    animate: {\n      backgroundPosition: [\"0, 50%\", \"100% 50%\", \"0 50%\"],\n    },\n  };\n  \n  return (\n    <div className={cn(\"relative p-[4px] group\", containerClassName)}>\n      <motion.div\n        variants={animate ? variants : undefined}\n        initial={animate ? \"initial\" : undefined}\n        animate={animate ? \"animate\" : undefined}\n        transition={\n          animate\n            ? {\n                duration: 5,\n                repeat: Infinity,\n                repeatType: \"reverse\",\n              }\n            : undefined\n        }\n        style={{\n          backgroundSize: animate ? \"400% 400%\" : undefined,\n        }}\n        className={cn(\n          \"absolute inset-0 rounded-3xl z-[1] opacity-60 group-hover:opacity-100 blur-xl transition duration-500 will-change-transform\",\n          \"bg-[radial-gradient(circle_farthest-side_at_0_100%,#0ea5e9,transparent),radial-gradient(circle_farthest-side_at_100%_0,#eab308,transparent),radial-gradient(circle_farthest-side_at_100%_100%,#0284c7,transparent),radial-gradient(circle_farthest-side_at_0_0,#38bdf8,#141316)]\"\n        )}\n      />\n      <motion.div\n        variants={animate ? variants : undefined}\n        initial={animate ? \"initial\" : undefined}\n        animate={animate ? \"animate\" : undefined}\n        transition={\n          animate\n            ? {\n                duration: 5,\n                repeat: Infinity,\n                repeatType: \"reverse\",\n              }\n            : undefined\n        }\n        style={{\n          backgroundSize: animate ? \"400% 400%\" : undefined,\n        }}\n        className={cn(\n          \"absolute inset-0 rounded-3xl z-[1] will-change-transform\",\n          \"bg-[radial-gradient(circle_farthest-side_at_0_100%,#0ea5e9,transparent),radial-gradient(circle_farthest-side_at_100%_0,#eab308,transparent),radial-gradient(circle_farthest-side_at_100%_100%,#0284c7,transparent),radial-gradient(circle_farthest-side_at_0_0,#38bdf8,#141316)]\"\n        )}\n      />\n\n      <div className={cn(\"relative z-10\", className)}>{children}</div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAMO,MAAM,qBAAqB,CAAC,EACjC,QAAQ,EACR,SAAS,EACT,kBAAkB,EAClB,UAAU,IAAI,EAMf;IACC,MAAM,WAAW;QACf,SAAS;YACP,oBAAoB;QACtB;QACA,SAAS;YACP,oBAAoB;gBAAC;gBAAU;gBAAY;aAAQ;QACrD;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;;0BAC3C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU,UAAU,WAAW;gBAC/B,SAAS,UAAU,YAAY;gBAC/B,SAAS,UAAU,YAAY;gBAC/B,YACE,UACI;oBACE,UAAU;oBACV,QAAQ;oBACR,YAAY;gBACd,IACA;gBAEN,OAAO;oBACL,gBAAgB,UAAU,cAAc;gBAC1C;gBACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+HACA;;;;;;0BAGJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU,UAAU,WAAW;gBAC/B,SAAS,UAAU,YAAY;gBAC/B,SAAS,UAAU,YAAY;gBAC/B,YACE,UACI;oBACE,UAAU;oBACV,QAAQ;oBACR,YAAY;gBACd,IACA;gBAEN,OAAO;oBACL,gBAAgB,UAAU,cAAc;gBAC1C;gBACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;;;;;;0BAIJ,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;0BAAa;;;;;;;;;;;;AAGvD", "debugId": null}}, {"offset": {"line": 1696, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/sections/hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"motion/react\";\nimport { TextGenerateEffect } from \"@/components/ui/text-generate-effect\";\nimport { BackgroundGradient } from \"@/components/ui/background-gradient\";\nimport { HoverBorderGradient } from \"@/components/ui/hover-border-gradient\";\nimport { fadeInUp, staggerContainer } from \"@/lib/utils\";\nimport { ArrowRight, Star, Shield, Award } from \"lucide-react\";\n\nexport function Hero() {\n  const heroText = \"Automotive Artistry That Protects Your Investment\";\n  const subText = \"Premium detailing services with meticulous attention to every corner of your vehicle. From ceramic coating to paint correction, we deliver results that exceed expectations.\";\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden hero-background\">\n      {/* Enhanced Background Elements */}\n      <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n      <div className=\"absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-blue-400/30 to-blue-600/20 rounded-full blur-3xl animate-float glow-effect\"></div>\n      <div className=\"absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-yellow-400/30 to-yellow-600/20 rounded-full blur-3xl animate-float glow-effect-accent\" style={{ animationDelay: \"2s\" }}></div>\n      <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/5 to-yellow-500/5 rounded-full blur-3xl\"></div>\n      \n      <div className=\"container mx-auto px-4 relative z-10\">\n        <motion.div\n          variants={staggerContainer}\n          initial=\"initial\"\n          animate=\"animate\"\n          className=\"text-center max-w-6xl mx-auto\"\n        >\n          {/* Trust Indicators */}\n          <motion.div\n            variants={fadeInUp}\n            className=\"flex items-center justify-center gap-6 mb-8\"\n          >\n            <div className=\"flex items-center gap-2 text-sm text-neutral-600\">\n              <Star className=\"w-4 h-4 fill-yellow-400 text-yellow-400\" />\n              <span>5.0 Rating</span>\n            </div>\n            <div className=\"flex items-center gap-2 text-sm text-neutral-600\">\n              <Shield className=\"w-4 h-4 text-blue-600\" />\n              <span>Fully Insured</span>\n            </div>\n            <div className=\"flex items-center gap-2 text-sm text-neutral-600\">\n              <Award className=\"w-4 h-4 text-yellow-600\" />\n              <span>Certified Professional</span>\n            </div>\n          </motion.div>\n\n          {/* Enhanced Main Heading */}\n          <motion.div variants={fadeInUp} className=\"mb-8\">\n            <TextGenerateEffect\n              words={heroText}\n              className=\"text-4xl md:text-6xl lg:text-8xl font-bold text-center luxury-text-gradient leading-tight\"\n            />\n          </motion.div>\n\n          {/* Enhanced Subheading */}\n          <motion.p\n            variants={fadeInUp}\n            className=\"text-lg md:text-xl lg:text-2xl text-neutral-700 max-w-4xl mx-auto mb-12 leading-relaxed font-medium\"\n          >\n            {subText}\n          </motion.p>\n\n          {/* Enhanced CTA Buttons */}\n          <motion.div\n            variants={fadeInUp}\n            className=\"flex flex-col sm:flex-row gap-6 justify-center items-center mb-16\"\n          >\n            <BackgroundGradient className=\"rounded-full\">\n              <button className=\"bg-white text-neutral-800 px-10 py-5 rounded-full font-bold text-lg flex items-center gap-3 luxury-hover magnetic-hover\">\n                Get Free Quote\n                <ArrowRight className=\"w-5 h-5 transition-transform duration-300 group-hover:translate-x-1\" />\n              </button>\n            </BackgroundGradient>\n\n            <HoverBorderGradient\n              containerClassName=\"rounded-full\"\n              className=\"bg-transparent text-neutral-800 px-10 py-5 font-bold text-lg magnetic-hover\"\n            >\n              View Our Work\n            </HoverBorderGradient>\n          </motion.div>\n\n          {/* Enhanced Before/After Preview */}\n          <motion.div\n            variants={fadeInUp}\n            className=\"grid md:grid-cols-2 gap-8 max-w-5xl mx-auto\"\n          >\n            <div className=\"relative group\">\n              <div className=\"absolute inset-0 bg-gradient-to-r from-red-500/30 to-orange-500/30 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500\"></div>\n              <div className=\"relative glass-card rounded-3xl p-8 border border-white/30 luxury-shadow group-hover:luxury-shadow-lg transition-all duration-500\">\n                <div className=\"aspect-video bg-gradient-to-br from-red-100 to-orange-100 rounded-2xl mb-6 flex items-center justify-center relative overflow-hidden\">\n                  <div className=\"absolute top-3 left-3 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold\">\n                    BEFORE\n                  </div>\n                  <span className=\"text-red-600 font-semibold text-lg\">Dull & Faded</span>\n                </div>\n                <h3 className=\"font-bold text-neutral-800 mb-3 text-xl\">Dull & Oxidized</h3>\n                <p className=\"text-neutral-600 leading-relaxed\">Faded paint, water spots, and surface contamination diminish your vehicle's appearance</p>\n              </div>\n            </div>\n\n            <div className=\"relative group\">\n              <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/30 to-green-500/30 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500\"></div>\n              <div className=\"relative glass-card rounded-3xl p-8 border border-white/30 luxury-shadow group-hover:luxury-shadow-lg transition-all duration-500\">\n                <div className=\"aspect-video bg-gradient-to-br from-blue-100 to-green-100 rounded-2xl mb-6 flex items-center justify-center relative overflow-hidden\">\n                  <div className=\"absolute top-3 right-3 bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold\">\n                    AFTER\n                  </div>\n                  <span className=\"text-green-600 font-semibold text-lg\">Showroom Ready</span>\n                </div>\n                <h3 className=\"font-bold text-neutral-800 mb-3 text-xl\">Showroom Shine</h3>\n                <p className=\"text-neutral-600 leading-relaxed\">Deep gloss, protected finish, and mirror-like clarity that exceeds expectations</p>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 2, duration: 1 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <div className=\"flex flex-col items-center gap-2 text-neutral-400\">\n          <span className=\"text-sm\">Scroll to explore</span>\n          <motion.div\n            animate={{ y: [0, 10, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-6 h-10 border-2 border-neutral-300 rounded-full flex justify-center\"\n          >\n            <motion.div\n              animate={{ y: [0, 12, 0] }}\n              transition={{ duration: 2, repeat: Infinity }}\n              className=\"w-1 h-3 bg-neutral-400 rounded-full mt-2\"\n            />\n          </motion.div>\n        </div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AASO,SAAS;IACd,MAAM,WAAW;IACjB,MAAM,UAAU;IAEhB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;gBAAoJ,OAAO;oBAAE,gBAAgB;gBAAK;;;;;;0BACjM,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU,mHAAA,CAAA,mBAAgB;oBAC1B,SAAQ;oBACR,SAAQ;oBACR,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU,mHAAA,CAAA,WAAQ;4BAClB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU,mHAAA,CAAA,WAAQ;4BAAE,WAAU;sCACxC,cAAA,8OAAC,sJAAA,CAAA,qBAAkB;gCACjB,OAAO;gCACP,WAAU;;;;;;;;;;;sCAKd,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU,mHAAA,CAAA,WAAQ;4BAClB,WAAU;sCAET;;;;;;sCAIH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU,mHAAA,CAAA,WAAQ;4BAClB,WAAU;;8CAEV,8OAAC,kJAAA,CAAA,qBAAkB;oCAAC,WAAU;8CAC5B,cAAA,8OAAC;wCAAO,WAAU;;4CAA0H;0DAE1I,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAI1B,8OAAC,uJAAA,CAAA,sBAAmB;oCAClB,oBAAmB;oCACnB,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU,mHAAA,CAAA,WAAQ;4BAClB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAuF;;;;;;sEAGtG,8OAAC;4DAAK,WAAU;sEAAqC;;;;;;;;;;;;8DAEvD,8OAAC;oDAAG,WAAU;8DAA0C;;;;;;8DACxD,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;8CAIpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA0F;;;;;;sEAGzG,8OAAC;4DAAK,WAAU;sEAAuC;;;;;;;;;;;;8DAEzD,8OAAC;oDAAG,WAAU;8DAA0C;;;;;;8DACxD,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAG,UAAU;gBAAE;gBACpC,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;oCAAC;oCAAG;oCAAI;iCAAE;4BAAC;4BACzB,YAAY;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;4BAC5C,WAAU;sCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACzB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;gCAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB", "debugId": null}}, {"offset": {"line": 2147, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/ui/card-hover-effect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport { AnimatePresence, motion } from \"motion/react\";\nimport { useState } from \"react\";\n\nexport const HoverEffect = ({\n  items,\n  className,\n}: {\n  items: {\n    title: string;\n    description: string;\n    link?: string;\n    icon?: React.ReactNode;\n  }[];\n  className?: string;\n}) => {\n  let [hoveredIndex, setHoveredIndex] = useState<number | null>(null);\n\n  return (\n    <div\n      className={cn(\n        \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 py-10\",\n        className\n      )}\n    >\n      {items.map((item, idx) => (\n        <div\n          key={item?.link || idx}\n          className=\"relative group block p-2 h-full w-full\"\n          onMouseEnter={() => setHoveredIndex(idx)}\n          onMouseLeave={() => setHoveredIndex(null)}\n        >\n          <AnimatePresence>\n            {hoveredIndex === idx && (\n              <motion.span\n                className=\"absolute inset-0 h-full w-full bg-neutral-200 dark:bg-slate-800/[0.8] block rounded-3xl\"\n                layoutId=\"hoverBackground\"\n                initial={{ opacity: 0 }}\n                animate={{\n                  opacity: 1,\n                  transition: { duration: 0.15 },\n                }}\n                exit={{\n                  opacity: 0,\n                  transition: { duration: 0.15, delay: 0.2 },\n                }}\n              />\n            )}\n          </AnimatePresence>\n          <Card>\n            {item.icon && (\n              <div className=\"flex items-center justify-center w-12 h-12 mb-4 rounded-lg bg-gradient-to-br from-blue-500 to-yellow-500\">\n                {item.icon}\n              </div>\n            )}\n            <CardTitle>{item.title}</CardTitle>\n            <CardDescription>{item.description}</CardDescription>\n          </Card>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport const Card = ({\n  className,\n  children,\n}: {\n  className?: string;\n  children: React.ReactNode;\n}) => {\n  return (\n    <div\n      className={cn(\n        \"rounded-3xl h-full w-full p-6 overflow-hidden glass-card border border-white/30 group-hover:border-white/50 relative z-20 card-shadow group-hover:luxury-shadow-lg transition-all duration-500\",\n        className\n      )}\n    >\n      <div className=\"relative z-50\">\n        <div className=\"p-2\">{children}</div>\n      </div>\n    </div>\n  );\n};\n\nexport const CardTitle = ({\n  className,\n  children,\n}: {\n  className?: string;\n  children: React.ReactNode;\n}) => {\n  return (\n    <h4 className={cn(\"text-neutral-800 dark:text-neutral-100 font-bold tracking-wide mt-6 text-xl\", className)}>\n      {children}\n    </h4>\n  );\n};\n\nexport const CardDescription = ({\n  className,\n  children,\n}: {\n  className?: string;\n  children: React.ReactNode;\n}) => {\n  return (\n    <p\n      className={cn(\n        \"mt-4 text-neutral-600 dark:text-neutral-400 tracking-wide leading-relaxed text-base\",\n        className\n      )}\n    >\n      {children}\n    </p>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMO,MAAM,cAAc,CAAC,EAC1B,KAAK,EACL,SAAS,EASV;IACC,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;kBAGD,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,8OAAC;gBAEC,WAAU;gBACV,cAAc,IAAM,gBAAgB;gBACpC,cAAc,IAAM,gBAAgB;;kCAEpC,8OAAC,yLAAA,CAAA,kBAAe;kCACb,iBAAiB,qBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,WAAU;4BACV,UAAS;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCACP,SAAS;gCACT,YAAY;oCAAE,UAAU;gCAAK;4BAC/B;4BACA,MAAM;gCACJ,SAAS;gCACT,YAAY;oCAAE,UAAU;oCAAM,OAAO;gCAAI;4BAC3C;;;;;;;;;;;kCAIN,8OAAC;;4BACE,KAAK,IAAI,kBACR,8OAAC;gCAAI,WAAU;0CACZ,KAAK,IAAI;;;;;;0CAGd,8OAAC;0CAAW,KAAK,KAAK;;;;;;0CACtB,8OAAC;0CAAiB,KAAK,WAAW;;;;;;;;;;;;;eA7B/B,MAAM,QAAQ;;;;;;;;;;AAmC7B;AAEO,MAAM,OAAO,CAAC,EACnB,SAAS,EACT,QAAQ,EAIT;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kMACA;kBAGF,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAO;;;;;;;;;;;;;;;;AAI9B;AAEO,MAAM,YAAY,CAAC,EACxB,SAAS,EACT,QAAQ,EAIT;IACC,qBACE,8OAAC;QAAG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+EAA+E;kBAC9F;;;;;;AAGP;AAEO,MAAM,kBAAkB,CAAC,EAC9B,SAAS,EACT,QAAQ,EAIT;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uFACA;kBAGD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 2294, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/sections/services.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"motion/react\";\nimport { HoverEffect } from \"@/components/ui/card-hover-effect\";\nimport { scrollFadeIn, createScrollFadeIn } from \"@/lib/utils\";\nimport { \n  Sparkles, \n  Shield, \n  Truck, \n  Paintbrush, \n  Droplets, \n  Car \n} from \"lucide-react\";\n\nexport function Services() {\n  const services = [\n    {\n      title: \"Ceramic Coating\",\n      description: \"Advanced nano-ceramic protection that bonds to your paint, providing years of protection against UV rays, chemicals, and environmental contaminants.\",\n      icon: <Shield className=\"w-6 h-6 text-white\" />,\n    },\n    {\n      title: \"Paint Correction\",\n      description: \"Multi-stage polishing process to remove swirl marks, scratches, and oxidation, restoring your vehicle's paint to better-than-new condition.\",\n      icon: <Paintbrush className=\"w-6 h-6 text-white\" />,\n    },\n    {\n      title: \"Mobile Detailing\",\n      description: \"Professional detailing services brought directly to your location. Convenience without compromising on quality or attention to detail.\",\n      icon: <Truck className=\"w-6 h-6 text-white\" />,\n    },\n    {\n      title: \"Interior Deep Clean\",\n      description: \"Comprehensive interior restoration including leather conditioning, fabric protection, and steam cleaning for a fresh, pristine cabin.\",\n      icon: <Sparkles className=\"w-6 h-6 text-white\" />,\n    },\n    {\n      title: \"Hydrophobic Treatment\",\n      description: \"Advanced water-repelling technology that makes maintenance easier while providing superior protection against water damage and staining.\",\n      icon: <Droplets className=\"w-6 h-6 text-white\" />,\n    },\n    {\n      title: \"Full Vehicle Restoration\",\n      description: \"Complete transformation service combining all our expertise to bring neglected vehicles back to showroom condition and beyond.\",\n      icon: <Car className=\"w-6 h-6 text-white\" />,\n    },\n  ];\n\n  return (\n    <section className=\"py-24 bg-gradient-to-b from-white via-neutral-50/50 to-white relative overflow-hidden\">\n      {/* Enhanced Background Elements */}\n      <div className=\"absolute top-0 left-0 w-full h-2 luxury-gradient\"></div>\n      <div className=\"absolute top-10 right-10 w-64 h-64 bg-gradient-to-r from-blue-400/10 to-yellow-400/10 rounded-full blur-3xl\"></div>\n      <div className=\"absolute bottom-10 left-10 w-80 h-80 bg-gradient-to-r from-yellow-400/10 to-blue-400/10 rounded-full blur-3xl\"></div>\n      \n      <div className=\"container mx-auto px-4\">\n        <motion.div\n          {...scrollFadeIn}\n          className=\"text-center mb-16\"\n        >\n          <motion.span\n            {...scrollFadeIn}\n            className=\"inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-4\"\n          >\n            Our Services\n          </motion.span>\n          \n          <motion.h2\n            {...scrollFadeIn}\n            className=\"text-4xl md:text-5xl lg:text-7xl font-bold mb-6\"\n          >\n            <span className=\"luxury-text-gradient\">Four Corners of</span>\n            <br />\n            <span className=\"bg-gradient-to-r from-blue-600 to-yellow-600 bg-clip-text text-transparent\">Excellence</span>\n          </motion.h2>\n          \n          <motion.p\n            {...scrollFadeIn}\n            className=\"text-lg md:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed\"\n          >\n            Every vehicle receives our comprehensive four-corner approach: meticulous preparation, \n            expert application, quality assurance, and ongoing protection. No detail overlooked, \n            no corner cut.\n          </motion.p>\n        </motion.div>\n\n        <motion.div\n          {...scrollFadeIn}\n          className=\"mb-16\"\n        >\n          <HoverEffect items={services} className=\"max-w-6xl mx-auto\" />\n        </motion.div>\n\n        {/* Enhanced Process Overview */}\n        <motion.div\n          {...scrollFadeIn}\n          className=\"glass-card rounded-3xl p-8 md:p-12 max-w-5xl mx-auto luxury-shadow-lg border border-white/30\"\n        >\n          <h3 className=\"text-3xl md:text-4xl font-bold text-center mb-12\">\n            <span className=\"luxury-text-gradient\">Our Four-Corner Process</span>\n          </h3>\n\n          <div className=\"grid md:grid-cols-4 gap-8\">\n            {[\n              { number: \"01\", title: \"Assessment\", description: \"Thorough inspection and consultation\", icon: \"🔍\" },\n              { number: \"02\", title: \"Preparation\", description: \"Meticulous cleaning and surface prep\", icon: \"🧽\" },\n              { number: \"03\", title: \"Application\", description: \"Expert technique and premium products\", icon: \"✨\" },\n              { number: \"04\", title: \"Protection\", description: \"Final inspection and care instructions\", icon: \"🛡️\" },\n            ].map((step, index) => (\n              <motion.div\n                key={index}\n                {...createScrollFadeIn(index * 0.1)}\n                className=\"text-center group\"\n              >\n                <div className=\"relative mb-6\">\n                  <div className=\"w-20 h-20 luxury-gradient rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto luxury-shadow group-hover:luxury-shadow-lg transition-all duration-300 group-hover:scale-105\">\n                    {step.number}\n                  </div>\n                  <div className=\"absolute -top-2 -right-2 text-2xl\">\n                    {step.icon}\n                  </div>\n                </div>\n                <h4 className=\"font-bold text-neutral-800 mb-3 text-lg\">{step.title}</h4>\n                <p className=\"text-neutral-600 leading-relaxed\">{step.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAcO,SAAS;IACd,MAAM,WAAW;QACf;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC1B;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,8MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC9B;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC5B;QACA;YACE,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;QACvB;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACR,GAAG,mHAAA,CAAA,eAAY;wBAChB,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gCACT,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;0CACX;;;;;;0CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACP,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAuB;;;;;;kDACvC,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAA6E;;;;;;;;;;;;0CAG/F,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACN,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;0CACX;;;;;;;;;;;;kCAOH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACR,GAAG,mHAAA,CAAA,eAAY;wBAChB,WAAU;kCAEV,cAAA,8OAAC,mJAAA,CAAA,cAAW;4BAAC,OAAO;4BAAU,WAAU;;;;;;;;;;;kCAI1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACR,GAAG,mHAAA,CAAA,eAAY;wBAChB,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAuB;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,QAAQ;wCAAM,OAAO;wCAAc,aAAa;wCAAwC,MAAM;oCAAK;oCACrG;wCAAE,QAAQ;wCAAM,OAAO;wCAAe,aAAa;wCAAwC,MAAM;oCAAK;oCACtG;wCAAE,QAAQ;wCAAM,OAAO;wCAAe,aAAa;wCAAyC,MAAM;oCAAI;oCACtG;wCAAE,QAAQ;wCAAM,OAAO;wCAAc,aAAa;wCAA0C,MAAM;oCAAM;iCACzG,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAER,GAAG,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,IAAI;wCACnC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,KAAK,MAAM;;;;;;kEAEd,8OAAC;wDAAI,WAAU;kEACZ,KAAK,IAAI;;;;;;;;;;;;0DAGd,8OAAC;gDAAG,WAAU;0DAA2C,KAAK,KAAK;;;;;;0DACnE,8OAAC;gDAAE,WAAU;0DAAoC,KAAK,WAAW;;;;;;;uCAb5D;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBrB", "debugId": null}}, {"offset": {"line": 2611, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/sections/testimonials.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"motion/react\";\nimport { scrollFadeIn, createScrollFadeIn } from \"@/lib/utils\";\nimport { Star, Quote } from \"lucide-react\";\n\nexport function Testimonials() {\n  const testimonials = [\n    {\n      name: \"<PERSON>\",\n      role: \"Tesla Model S Owner\",\n      content: \"<PERSON>'s attention to detail is absolutely incredible. My Tesla looks better than the day I bought it. The ceramic coating has been a game-changer for maintenance.\",\n      rating: 5,\n      image: \"/api/placeholder/60/60\",\n    },\n    {\n      name: \"<PERSON>\",\n      role: \"BMW M3 Enthusiast\",\n      content: \"I've tried many detailing services, but <PERSON>'s four-corner approach is unmatched. The paint correction work was flawless, and the results speak for themselves.\",\n      rating: 5,\n      image: \"/api/placeholder/60/60\",\n    },\n    {\n      name: \"<PERSON>\",\n      role: \"Luxury Car Collector\",\n      content: \"Professional, reliable, and the quality is consistently outstanding. <PERSON> treats every vehicle like it's his own. Highly recommend for anyone who values their investment.\",\n      rating: 5,\n      image: \"/api/placeholder/60/60\",\n    },\n    {\n      name: \"<PERSON>\",\n      role: \"Mercedes-Benz Owner\",\n      content: \"The mobile service is incredibly convenient, and the results are always perfect. <PERSON>'s expertise and premium products make all the difference.\",\n      rating: 5,\n      image: \"/api/placeholder/60/60\",\n    },\n  ];\n\n  const stats = [\n    { number: \"500+\", label: \"Vehicles Detailed\" },\n    { number: \"5.0\", label: \"Average Rating\" },\n    { number: \"100%\", label: \"Customer Satisfaction\" },\n    { number: \"3+\", label: \"Years Experience\" },\n  ];\n\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-white to-neutral-50 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute top-0 right-0 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl\"></div>\n      <div className=\"absolute bottom-0 left-0 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl\"></div>\n      \n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <motion.div\n          {...scrollFadeIn}\n          className=\"text-center mb-16\"\n        >\n          <motion.span\n            {...scrollFadeIn}\n            className=\"inline-block px-4 py-2 bg-yellow-100 text-yellow-800 rounded-full text-sm font-semibold mb-4\"\n          >\n            Client Testimonials\n          </motion.span>\n          \n          <motion.h2\n            {...scrollFadeIn}\n            className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-800 mb-6\"\n          >\n            What Our Clients\n            <span className=\"bg-gradient-to-r from-blue-600 to-yellow-600 bg-clip-text text-transparent\"> Say</span>\n          </motion.h2>\n          \n          <motion.p\n            {...scrollFadeIn}\n            className=\"text-lg md:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed\"\n          >\n            Don't just take our word for it. Here's what vehicle owners across the Four Corners region \n            say about our premium detailing services.\n          </motion.p>\n        </motion.div>\n\n        {/* Stats Section */}\n        <motion.div\n          {...scrollFadeIn}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mb-16\"\n        >\n          {stats.map((stat, index) => (\n            <motion.div\n              key={index}\n              {...createScrollFadeIn(index * 0.1)}\n              className=\"text-center\"\n            >\n              <div className=\"text-3xl md:text-4xl font-bold text-neutral-800 mb-2\">\n                {stat.number}\n              </div>\n              <div className=\"text-sm md:text-base text-neutral-600\">\n                {stat.label}\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Testimonials Grid */}\n        <motion.div\n          {...scrollFadeIn}\n          className=\"grid md:grid-cols-2 gap-8 max-w-6xl mx-auto\"\n        >\n          {testimonials.map((testimonial, index) => (\n            <motion.div\n              key={index}\n              {...createScrollFadeIn(index * 0.1)}\n              className=\"bg-white rounded-2xl p-8 shadow-lg border border-neutral-100 relative group hover:shadow-xl transition-all duration-300\"\n            >\n              {/* Quote Icon */}\n              <div className=\"absolute top-6 right-6 text-blue-200 group-hover:text-blue-300 transition-colors duration-300\">\n                <Quote className=\"w-8 h-8\" />\n              </div>\n\n              {/* Rating */}\n              <div className=\"flex items-center gap-1 mb-4\">\n                {[...Array(testimonial.rating)].map((_, i) => (\n                  <Star key={i} className=\"w-5 h-5 fill-yellow-400 text-yellow-400\" />\n                ))}\n              </div>\n\n              {/* Content */}\n              <p className=\"text-neutral-700 leading-relaxed mb-6 text-lg\">\n                \"{testimonial.content}\"\n              </p>\n\n              {/* Author */}\n              <div className=\"flex items-center gap-4\">\n                <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-yellow-500 rounded-full flex items-center justify-center text-white font-semibold\">\n                  {testimonial.name.split(' ').map(n => n[0]).join('')}\n                </div>\n                <div>\n                  <div className=\"font-semibold text-neutral-800\">\n                    {testimonial.name}\n                  </div>\n                  <div className=\"text-sm text-neutral-600\">\n                    {testimonial.role}\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Call to Action */}\n        <motion.div\n          {...scrollFadeIn}\n          className=\"text-center mt-16\"\n        >\n          <motion.div\n            {...scrollFadeIn}\n            className=\"bg-gradient-to-r from-blue-50 to-yellow-50 rounded-3xl p-8 md:p-12 max-w-4xl mx-auto\"\n          >\n            <h3 className=\"text-2xl md:text-3xl font-bold text-neutral-800 mb-4\">\n              Ready to Join Our Satisfied Clients?\n            </h3>\n            <p className=\"text-lg text-neutral-600 mb-8 max-w-2xl mx-auto\">\n              Experience the difference that professional, artisan-level detailing can make for your vehicle. \n              Get your free quote today.\n            </p>\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"bg-gradient-to-r from-blue-600 to-yellow-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n            >\n              Get Your Free Quote\n            </motion.button>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,eAAe;QACnB;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;YACR,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;YACR,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;YACR,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,QAAQ;YACR,OAAO;QACT;KACD;IAED,MAAM,QAAQ;QACZ;YAAE,QAAQ;YAAQ,OAAO;QAAoB;QAC7C;YAAE,QAAQ;YAAO,OAAO;QAAiB;QACzC;YAAE,QAAQ;YAAQ,OAAO;QAAwB;QACjD;YAAE,QAAQ;YAAM,OAAO;QAAmB;KAC3C;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACR,GAAG,mHAAA,CAAA,eAAY;wBAChB,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gCACT,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;0CACX;;;;;;0CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACP,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;;oCACX;kDAEC,8OAAC;wCAAK,WAAU;kDAA6E;;;;;;;;;;;;0CAG/F,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACN,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;0CACX;;;;;;;;;;;;kCAOH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACR,GAAG,mHAAA,CAAA,eAAY;wBAChB,WAAU;kCAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAER,GAAG,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,IAAI;gCACnC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACZ,KAAK,MAAM;;;;;;kDAEd,8OAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;;+BARR;;;;;;;;;;kCAeX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACR,GAAG,mHAAA,CAAA,eAAY;wBAChB,WAAU;kCAET,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAER,GAAG,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,IAAI;gCACnC,WAAU;;kDAGV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAInB,8OAAC;wCAAI,WAAU;kDACZ;+CAAI,MAAM,YAAY,MAAM;yCAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,8OAAC,kMAAA,CAAA,OAAI;gDAAS,WAAU;+CAAb;;;;;;;;;;kDAKf,8OAAC;wCAAE,WAAU;;4CAAgD;4CACzD,YAAY,OAAO;4CAAC;;;;;;;kDAIxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;0DAEnD,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEACZ,YAAY,IAAI;;;;;;kEAEnB,8OAAC;wDAAI,WAAU;kEACZ,YAAY,IAAI;;;;;;;;;;;;;;;;;;;+BA/BlB;;;;;;;;;;kCAwCX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACR,GAAG,mHAAA,CAAA,eAAY;wBAChB,WAAU;kCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACR,GAAG,mHAAA,CAAA,eAAY;4BAChB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAuD;;;;;;8CAGrE,8OAAC;oCAAE,WAAU;8CAAkD;;;;;;8CAI/D,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 2942, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/sections/gallery.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { motion, AnimatePresence } from \"motion/react\";\nimport { scrollFadeIn } from \"@/lib/utils\";\nimport { ChevronLeft, ChevronRight, Eye } from \"lucide-react\";\n\nexport function Gallery() {\n  const [selectedCategory, setSelectedCategory] = useState(\"all\");\n  const [selectedImage, setSelectedImage] = useState<number | null>(null);\n\n  const categories = [\n    { id: \"all\", name: \"All Work\" },\n    { id: \"ceramic\", name: \"Ceramic Coating\" },\n    { id: \"correction\", name: \"Paint Correction\" },\n    { id: \"interior\", name: \"Interior Detail\" },\n    { id: \"luxury\", name: \"Luxury Vehicles\" },\n  ];\n\n  const galleryItems = [\n    {\n      id: 1,\n      category: \"ceramic\",\n      title: \"Tesla Model S - Ceramic Coating\",\n      before: \"/api/placeholder/400/300\",\n      after: \"/api/placeholder/400/300\",\n      description: \"9H ceramic coating application with paint correction\",\n    },\n    {\n      id: 2,\n      category: \"correction\",\n      title: \"BMW M3 - Paint Correction\",\n      before: \"/api/placeholder/400/300\",\n      after: \"/api/placeholder/400/300\",\n      description: \"Multi-stage paint correction removing years of swirl marks\",\n    },\n    {\n      id: 3,\n      category: \"luxury\",\n      title: \"Mercedes S-Class - Full Detail\",\n      before: \"/api/placeholder/400/300\",\n      after: \"/api/placeholder/400/300\",\n      description: \"Complete exterior and interior restoration\",\n    },\n    {\n      id: 4,\n      category: \"interior\",\n      title: \"Porsche 911 - Interior Detail\",\n      before: \"/api/placeholder/400/300\",\n      after: \"/api/placeholder/400/300\",\n      description: \"Leather conditioning and fabric protection\",\n    },\n    {\n      id: 5,\n      category: \"ceramic\",\n      title: \"Audi RS6 - Ceramic Protection\",\n      before: \"/api/placeholder/400/300\",\n      after: \"/api/placeholder/400/300\",\n      description: \"Premium ceramic coating with 5-year warranty\",\n    },\n    {\n      id: 6,\n      category: \"correction\",\n      title: \"Lamborghini Huracán - Paint Restoration\",\n      before: \"/api/placeholder/400/300\",\n      after: \"/api/placeholder/400/300\",\n      description: \"Exotic car paint correction and protection\",\n    },\n  ];\n\n  const filteredItems = selectedCategory === \"all\" \n    ? galleryItems \n    : galleryItems.filter(item => item.category === selectedCategory);\n\n  return (\n    <section id=\"gallery\" className=\"py-24 bg-gradient-to-b from-white via-neutral-50/30 to-white relative overflow-hidden\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <motion.div\n          {...scrollFadeIn}\n          className=\"text-center mb-16\"\n        >\n          <motion.span\n            {...scrollFadeIn}\n            className=\"inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-4\"\n          >\n            Our Work\n          </motion.span>\n          \n          <motion.h2\n            {...scrollFadeIn}\n            className=\"text-4xl md:text-5xl lg:text-7xl font-bold mb-6\"\n          >\n            <span className=\"luxury-text-gradient\">Before & After</span>\n            <br />\n            <span className=\"bg-gradient-to-r from-blue-600 to-yellow-600 bg-clip-text text-transparent\">Gallery</span>\n          </motion.h2>\n          \n          <motion.p\n            {...scrollFadeIn}\n            className=\"text-lg md:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed\"\n          >\n            See the dramatic transformations we achieve through our meticulous four-corner approach. \n            Every vehicle tells a story of restoration and protection.\n          </motion.p>\n        </motion.div>\n\n        {/* Category Filter */}\n        <motion.div\n          {...scrollFadeIn}\n          className=\"flex flex-wrap justify-center gap-4 mb-12\"\n        >\n          {categories.map((category) => (\n            <motion.button\n              key={category.id}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setSelectedCategory(category.id)}\n              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${\n                selectedCategory === category.id\n                  ? \"bg-gradient-to-r from-blue-600 to-yellow-600 text-white shadow-lg\"\n                  : \"bg-white text-neutral-700 border border-neutral-200 hover:border-blue-300\"\n              }`}\n            >\n              {category.name}\n            </motion.button>\n          ))}\n        </motion.div>\n\n        {/* Gallery Grid */}\n        <motion.div\n          layout\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\"\n        >\n          <AnimatePresence>\n            {filteredItems.map((item, index) => (\n              <motion.div\n                key={item.id}\n                layout\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                exit={{ opacity: 0, scale: 0.8 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                className=\"glass-card rounded-3xl overflow-hidden luxury-shadow border border-white/30 group hover:luxury-shadow-lg transition-all duration-500\"\n              >\n                {/* Before/After Images */}\n                <div className=\"relative aspect-video overflow-hidden\">\n                  <div className=\"grid grid-cols-2 h-full\">\n                    {/* Before */}\n                    <div className=\"relative bg-gradient-to-br from-red-100 to-orange-100 flex items-center justify-center\">\n                      <div className=\"absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold\">\n                        BEFORE\n                      </div>\n                      <div className=\"text-red-600 font-semibold opacity-50\">\n                        Before Image\n                      </div>\n                    </div>\n                    \n                    {/* After */}\n                    <div className=\"relative bg-gradient-to-br from-blue-100 to-green-100 flex items-center justify-center\">\n                      <div className=\"absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold\">\n                        AFTER\n                      </div>\n                      <div className=\"text-green-600 font-semibold opacity-50\">\n                        After Image\n                      </div>\n                    </div>\n                  </div>\n                  \n                  {/* Hover Overlay */}\n                  <div className=\"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\">\n                    <motion.button\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                      onClick={() => setSelectedImage(item.id)}\n                      className=\"bg-white text-neutral-800 p-3 rounded-full shadow-lg\"\n                    >\n                      <Eye className=\"w-6 h-6\" />\n                    </motion.button>\n                  </div>\n                </div>\n\n                {/* Content */}\n                <div className=\"p-6\">\n                  <h3 className=\"font-bold text-neutral-800 mb-2 text-lg\">\n                    {item.title}\n                  </h3>\n                  <p className=\"text-neutral-600 text-sm leading-relaxed\">\n                    {item.description}\n                  </p>\n                </div>\n              </motion.div>\n            ))}\n          </AnimatePresence>\n        </motion.div>\n\n        {/* Call to Action */}\n        <motion.div\n          {...scrollFadeIn}\n          className=\"text-center mt-16\"\n        >\n          <motion.button\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            className=\"bg-gradient-to-r from-blue-600 to-yellow-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n          >\n            See More Work\n          </motion.button>\n        </motion.div>\n      </div>\n\n      {/* Modal for enlarged images would go here */}\n      <AnimatePresence>\n        {selectedImage && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4\"\n            onClick={() => setSelectedImage(null)}\n          >\n            <motion.div\n              initial={{ scale: 0.8 }}\n              animate={{ scale: 1 }}\n              exit={{ scale: 0.8 }}\n              className=\"bg-white rounded-2xl p-4 max-w-4xl w-full\"\n              onClick={(e) => e.stopPropagation()}\n            >\n              <div className=\"text-center\">\n                <h3 className=\"text-2xl font-bold mb-4\">\n                  {galleryItems.find(item => item.id === selectedImage)?.title}\n                </h3>\n                <div className=\"aspect-video bg-gradient-to-r from-neutral-100 to-neutral-200 rounded-xl flex items-center justify-center\">\n                  <span className=\"text-neutral-500\">Enlarged Before/After View</span>\n                </div>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAOO,SAAS;IACd,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;QAAW;QAC9B;YAAE,IAAI;YAAW,MAAM;QAAkB;QACzC;YAAE,IAAI;YAAc,MAAM;QAAmB;QAC7C;YAAE,IAAI;YAAY,MAAM;QAAkB;QAC1C;YAAE,IAAI;YAAU,MAAM;QAAkB;KACzC;IAED,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,UAAU;YACV,OAAO;YACP,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,UAAU;YACV,OAAO;YACP,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,UAAU;YACV,OAAO;YACP,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,UAAU;YACV,OAAO;YACP,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,UAAU;YACV,OAAO;YACP,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,UAAU;YACV,OAAO;YACP,QAAQ;YACR,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,gBAAgB,qBAAqB,QACvC,eACA,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IAElD,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;;0BAC9B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACR,GAAG,mHAAA,CAAA,eAAY;wBAChB,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gCACT,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;0CACX;;;;;;0CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACP,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAuB;;;;;;kDACvC,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAA6E;;;;;;;;;;;;0CAG/F,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACN,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;0CACX;;;;;;;;;;;;kCAOH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACR,GAAG,mHAAA,CAAA,eAAY;wBAChB,WAAU;kCAET,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS,IAAM,oBAAoB,SAAS,EAAE;gCAC9C,WAAW,CAAC,iEAAiE,EAC3E,qBAAqB,SAAS,EAAE,GAC5B,sEACA,6EACJ;0CAED,SAAS,IAAI;+BAVT,SAAS,EAAE;;;;;;;;;;kCAgBtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,MAAM;wBACN,WAAU;kCAEV,cAAA,8OAAC,yLAAA,CAAA,kBAAe;sCACb,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,MAAM;oCACN,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,MAAM;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAC/B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,WAAU;;sDAGV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAsF;;;;;;8EAGrG,8OAAC;oEAAI,WAAU;8EAAwC;;;;;;;;;;;;sEAMzD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAyF;;;;;;8EAGxG,8OAAC;oEAAI,WAAU;8EAA0C;;;;;;;;;;;;;;;;;;8DAO7D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,YAAY;4DAAE,OAAO;wDAAI;wDACzB,UAAU;4DAAE,OAAO;wDAAI;wDACvB,SAAS,IAAM,iBAAiB,KAAK,EAAE;wDACvC,WAAU;kEAEV,cAAA,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAMrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAEb,8OAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;;;;;;;;mCAnDhB,KAAK,EAAE;;;;;;;;;;;;;;;kCA4DpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACR,GAAG,mHAAA,CAAA,eAAY;wBAChB,WAAU;kCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,8OAAC,yLAAA,CAAA,kBAAe;0BACb,+BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS,IAAM,iBAAiB;8BAEhC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;wBAAI;wBACtB,SAAS;4BAAE,OAAO;wBAAE;wBACpB,MAAM;4BAAE,OAAO;wBAAI;wBACnB,WAAU;wBACV,SAAS,CAAC,IAAM,EAAE,eAAe;kCAEjC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,gBAAgB;;;;;;8CAEzD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD", "debugId": null}}, {"offset": {"line": 3396, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Client%20websites/Jon%20Four%20corner/jon-four-corners-detailing/src/components/sections/contact.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { motion } from \"motion/react\";\nimport { scrollFadeIn, createScrollFadeIn } from \"@/lib/utils\";\nimport { \n  Phone, \n  Mail, \n  MapPin, \n  Clock, \n  Send,\n  CheckCircle,\n  Car,\n  Calendar\n} from \"lucide-react\";\n\nexport function Contact() {\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    phone: \"\",\n    vehicle: \"\",\n    service: \"\",\n    message: \"\",\n  });\n  const [isSubmitted, setIsSubmitted] = useState(false);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Handle form submission here\n    setIsSubmitted(true);\n    setTimeout(() => setIsSubmitted(false), 3000);\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value,\n    });\n  };\n\n  const contactInfo = [\n    {\n      icon: <Phone className=\"w-6 h-6\" />,\n      title: \"Phone\",\n      details: \"(*************\",\n      subtitle: \"Call or text anytime\",\n    },\n    {\n      icon: <Mail className=\"w-6 h-6\" />,\n      title: \"Email\",\n      details: \"<EMAIL>\",\n      subtitle: \"Quick response guaranteed\",\n    },\n    {\n      icon: <MapPin className=\"w-6 h-6\" />,\n      title: \"Service Area\",\n      details: \"Four Corners Region\",\n      subtitle: \"Mobile service available\",\n    },\n    {\n      icon: <Clock className=\"w-6 h-6\" />,\n      title: \"Hours\",\n      details: \"Mon-Sat: 8AM-6PM\",\n      subtitle: \"Sunday by appointment\",\n    },\n  ];\n\n  const services = [\n    \"Ceramic Coating\",\n    \"Paint Correction\",\n    \"Interior Detail\",\n    \"Exterior Wash & Wax\",\n    \"Mobile Detailing\",\n    \"Full Vehicle Restoration\",\n    \"Other (specify in message)\",\n  ];\n\n  return (\n    <section id=\"contact\" className=\"py-20 bg-gradient-to-b from-white to-neutral-50 relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-yellow-500 to-blue-500\"></div>\n      \n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <motion.div\n          {...scrollFadeIn}\n          className=\"text-center mb-16\"\n        >\n          <motion.span\n            {...scrollFadeIn}\n            className=\"inline-block px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-semibold mb-4\"\n          >\n            Get In Touch\n          </motion.span>\n          \n          <motion.h2\n            {...scrollFadeIn}\n            className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-800 mb-6\"\n          >\n            Ready to Transform\n            <span className=\"bg-gradient-to-r from-blue-600 to-yellow-600 bg-clip-text text-transparent\"> Your Vehicle?</span>\n          </motion.h2>\n          \n          <motion.p\n            {...scrollFadeIn}\n            className=\"text-lg md:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed\"\n          >\n            Get your free quote today and discover why vehicle owners across the Four Corners region \n            trust us with their most valuable investments.\n          </motion.p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto\">\n          {/* Contact Form */}\n          <motion.div\n            {...scrollFadeIn}\n            className=\"bg-white rounded-3xl p-8 shadow-xl border border-neutral-100\"\n          >\n            <div className=\"flex items-center gap-3 mb-6\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-yellow-500 rounded-full flex items-center justify-center\">\n                <Send className=\"w-6 h-6 text-white\" />\n              </div>\n              <div>\n                <h3 className=\"text-2xl font-bold text-neutral-800\">Get Your Free Quote</h3>\n                <p className=\"text-neutral-600\">We'll respond within 24 hours</p>\n              </div>\n            </div>\n\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div className=\"grid md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-semibold text-neutral-700 mb-2\">\n                    Full Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleChange}\n                    required\n                    className=\"w-full px-4 py-3 border border-neutral-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                    placeholder=\"Your name\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-semibold text-neutral-700 mb-2\">\n                    Phone Number *\n                  </label>\n                  <input\n                    type=\"tel\"\n                    name=\"phone\"\n                    value={formData.phone}\n                    onChange={handleChange}\n                    required\n                    className=\"w-full px-4 py-3 border border-neutral-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                    placeholder=\"(*************\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-semibold text-neutral-700 mb-2\">\n                  Email Address *\n                </label>\n                <input\n                  type=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  required\n                  className=\"w-full px-4 py-3 border border-neutral-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-semibold text-neutral-700 mb-2\">\n                  Vehicle Information\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"vehicle\"\n                  value={formData.vehicle}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-3 border border-neutral-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                  placeholder=\"Year, Make, Model (e.g., 2023 Tesla Model S)\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-semibold text-neutral-700 mb-2\">\n                  Service Needed\n                </label>\n                <select\n                  name=\"service\"\n                  value={formData.service}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-3 border border-neutral-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                >\n                  <option value=\"\">Select a service</option>\n                  {services.map((service) => (\n                    <option key={service} value={service}>\n                      {service}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-semibold text-neutral-700 mb-2\">\n                  Additional Details\n                </label>\n                <textarea\n                  name=\"message\"\n                  value={formData.message}\n                  onChange={handleChange}\n                  rows={4}\n                  className=\"w-full px-4 py-3 border border-neutral-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none\"\n                  placeholder=\"Tell us about your vehicle's condition, specific concerns, or any questions you have...\"\n                />\n              </div>\n\n              <motion.button\n                type=\"submit\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n                disabled={isSubmitted}\n                className=\"w-full bg-gradient-to-r from-blue-600 to-yellow-600 text-white py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50\"\n              >\n                {isSubmitted ? (\n                  <span className=\"flex items-center justify-center gap-2\">\n                    <CheckCircle className=\"w-5 h-5\" />\n                    Message Sent!\n                  </span>\n                ) : (\n                  <span className=\"flex items-center justify-center gap-2\">\n                    <Send className=\"w-5 h-5\" />\n                    Get My Free Quote\n                  </span>\n                )}\n              </motion.button>\n            </form>\n          </motion.div>\n\n          {/* Contact Information */}\n          <motion.div\n            {...scrollFadeIn}\n            className=\"space-y-8\"\n          >\n            {/* Contact Cards */}\n            <div className=\"grid gap-6\">\n              {contactInfo.map((info, index) => (\n                <motion.div\n                  key={index}\n                  {...createScrollFadeIn(index * 0.1)}\n                  className=\"bg-white rounded-2xl p-6 shadow-lg border border-neutral-100 hover:shadow-xl transition-all duration-300\"\n                >\n                  <div className=\"flex items-center gap-4\">\n                    <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-yellow-500 rounded-full flex items-center justify-center text-white\">\n                      {info.icon}\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-neutral-800 text-lg\">\n                        {info.title}\n                      </h4>\n                      <p className=\"text-neutral-900 font-medium\">\n                        {info.details}\n                      </p>\n                      <p className=\"text-sm text-neutral-600\">\n                        {info.subtitle}\n                      </p>\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Quick Actions */}\n            <motion.div\n              {...scrollFadeIn}\n              className=\"bg-gradient-to-r from-blue-50 to-yellow-50 rounded-2xl p-6\"\n            >\n              <h4 className=\"font-bold text-neutral-800 text-lg mb-4\">\n                Quick Actions\n              </h4>\n              <div className=\"space-y-3\">\n                <motion.button\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  className=\"w-full bg-white text-neutral-800 py-3 rounded-xl font-semibold flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-300\"\n                >\n                  <Phone className=\"w-5 h-5\" />\n                  Call Now\n                </motion.button>\n                <motion.button\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  className=\"w-full bg-white text-neutral-800 py-3 rounded-xl font-semibold flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-300\"\n                >\n                  <Calendar className=\"w-5 h-5\" />\n                  Schedule Online\n                </motion.button>\n              </div>\n            </motion.div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,8BAA8B;QAC9B,eAAe;QACf,WAAW,IAAM,eAAe,QAAQ;IAC1C;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,cAAc;QAClB;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,SAAS;YACT,UAAU;QACZ;QACA;YACE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;YACP,SAAS;YACT,UAAU;QACZ;QACA;YACE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,SAAS;YACT,UAAU;QACZ;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,SAAS;YACT,UAAU;QACZ;KACD;IAED,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;;0BAE9B,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACR,GAAG,mHAAA,CAAA,eAAY;wBAChB,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gCACT,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;0CACX;;;;;;0CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACP,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;;oCACX;kDAEC,8OAAC;wCAAK,WAAU;kDAA6E;;;;;;;;;;;;0CAG/F,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACN,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;0CACX;;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACR,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,8OAAC;wDAAE,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;kDAIpC,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAoD;;;;;;0EAGrE,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAoD;;;;;;0EAGrE,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoD;;;;;;kEAGrE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,QAAQ;wDACR,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoD;;;;;;kEAGrE,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoD;;;;;;kEAGrE,8OAAC;wDACC,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;oEAAqB,OAAO;8EAC1B;mEADU;;;;;;;;;;;;;;;;;0DAOnB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoD;;;;;;kEAGrE,8OAAC;wDACC,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,MAAK;gDACL,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,UAAU;gDACV,WAAU;0DAET,4BACC,8OAAC;oDAAK,WAAU;;sEACd,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAAY;;;;;;yEAIrC,8OAAC;oDAAK,WAAU;;sEACd,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAStC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACR,GAAG,mHAAA,CAAA,eAAY;gCAChB,WAAU;;kDAGV,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAER,GAAG,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,IAAI;gDACnC,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,KAAK,IAAI;;;;;;sEAEZ,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EACX,KAAK,KAAK;;;;;;8EAEb,8OAAC;oEAAE,WAAU;8EACV,KAAK,OAAO;;;;;;8EAEf,8OAAC;oEAAE,WAAU;8EACV,KAAK,QAAQ;;;;;;;;;;;;;;;;;;+CAhBf;;;;;;;;;;kDAyBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACR,GAAG,mHAAA,CAAA,eAAY;wCAChB,WAAU;;0DAEV,8OAAC;gDAAG,WAAU;0DAA0C;;;;;;0DAGxD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;wDACxB,WAAU;;0EAEV,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAG/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;wDACxB,WAAU;;0EAEV,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlD", "debugId": null}}]}