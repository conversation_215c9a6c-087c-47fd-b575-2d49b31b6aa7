[{"name": "hot-reloader", "duration": 153, "timestamp": 11123076922, "id": 3, "tags": {"version": "15.3.4"}, "startTime": 1751057186838, "traceId": "4b7798ee6c774552"}, {"name": "setup-dev-bundler", "duration": 1104943, "timestamp": 11122777044, "id": 2, "parentId": 1, "tags": {}, "startTime": 1751057186539, "traceId": "4b7798ee6c774552"}, {"name": "run-instrumentation-hook", "duration": 25, "timestamp": 11123969622, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751057187731, "traceId": "4b7798ee6c774552"}, {"name": "start-dev-server", "duration": 1983843, "timestamp": 11122018734, "id": 1, "tags": {"cpus": "8", "platform": "win32", "memory.freeMem": "747933696", "memory.totalMem": "8215093248", "memory.heapSizeLimit": "4157603840", "memory.rss": "179240960", "memory.heapTotal": "97718272", "memory.heapUsed": "74421264"}, "startTime": 1751057185780, "traceId": "4b7798ee6c774552"}, {"name": "compile-path", "duration": 5685637, "timestamp": 11126491878, "id": 7, "tags": {"trigger": "/"}, "startTime": 1751057190253, "traceId": "4b7798ee6c774552"}, {"name": "ensure-page", "duration": 5688047, "timestamp": 11126491041, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1751057190253, "traceId": "4b7798ee6c774552"}]