@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');
@import "tailwindcss";

:root {
  /* Luxury Color Palette */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  --accent-50: #fefce8;
  --accent-100: #fef9c3;
  --accent-200: #fef08a;
  --accent-300: #fde047;
  --accent-400: #facc15;
  --accent-500: #eab308;
  --accent-600: #ca8a04;
  --accent-700: #a16207;
  --accent-800: #854d0e;
  --accent-900: #713f12;

  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;

  /* Legacy variables for compatibility */
  --background: var(--neutral-50);
  --foreground: var(--neutral-800);

  /* Gradients */
  --gradient-luxury: linear-gradient(135deg, var(--primary-600) 0%, var(--accent-500) 100%);
  --gradient-shimmer: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);

  /* Shadows */
  --shadow-luxury: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 20px rgba(14, 165, 233, 0.3);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-serif: 'Playfair Display', serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--neutral-900);
    --foreground: var(--neutral-100);
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--foreground);
  background: var(--background) !important;
  overflow-x: hidden;
  position: relative;
  z-index: 1;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-100);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-400);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-600);
}

@layer base {
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
  }
}

@layer components {
  .luxury-gradient {
    background: var(--gradient-luxury);
  }

  .luxury-shadow {
    box-shadow: var(--shadow-luxury);
  }

  .glow-effect {
    box-shadow: var(--shadow-glow);
  }

  .shimmer-effect {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-gpu {
    transform: translateZ(0);
  }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(14, 165, 233, 0.3); }
  50% { box-shadow: 0 0 30px rgba(14, 165, 233, 0.6); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

/* Fix for development overlay issues */
[data-nextjs-dialog-overlay] {
  display: none !important;
}

[data-nextjs-dialog-backdrop] {
  display: none !important;
}

.nextjs-toast {
  display: none !important;
}

/* Ensure no gray overlays */
body::before,
body::after {
  display: none !important;
}

/* Remove any potential modal backdrops */
.modal-backdrop,
.overlay,
.backdrop {
  display: none !important;
}
