import type { Metadata } from "next";
import { Inter, Playfair_Display } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const playfair = Playfair_Display({
  subsets: ["latin"],
  variable: "--font-playfair",
});

export const metadata: Metadata = {
  title: "Jon Four Corners Detailing | Premium Automotive Detailing Services",
  description: "Professional automotive detailing and reconditioning services. Specializing in ceramic coating, paint correction, and mobile detailing. Protecting your investment with artisan-level craftsmanship.",
  keywords: ["automotive detailing", "car detailing", "ceramic coating", "paint correction", "mobile detailing", "Four Corners", "premium car care"],
  authors: [{ name: "Jon Four Corners Detailing" }],
  openGraph: {
    title: "Jon Four Corners Detailing | Premium Automotive Detailing",
    description: "Professional automotive detailing services with artisan-level craftsmanship. Ceramic coating, paint correction, and mobile detailing.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Jon Four Corners Detailing | Premium Automotive Detailing",
    description: "Professional automotive detailing services with artisan-level craftsmanship.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${inter.variable} ${playfair.variable} antialiased min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100`}
      >
        <div className="relative">
          {children}
        </div>
      </body>
    </html>
  );
}
