"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "motion/react";
import { Menu, X, Phone, MapPin } from "lucide-react";
import { HoverBorderGradient } from "@/components/ui/hover-border-gradient";

export function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navItems = [
    { name: "Services", href: "#services" },
    { name: "Process", href: "#process" },
    { name: "Gallery", href: "#gallery" },
    { name: "About", href: "#about" },
    { name: "Contact", href: "#contact" },
  ];

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
        scrolled
          ? "backdrop-blur-luxury bg-white/80 luxury-shadow border-b border-white/30"
          : "bg-transparent"
      }`}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Logo */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            className="flex items-center space-x-3 magnetic-hover"
          >
            <div className="w-12 h-12 luxury-gradient rounded-xl flex items-center justify-center luxury-shadow">
              <span className="text-white font-bold text-xl">J</span>
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold text-neutral-800">
                Jon Four Corners
              </h1>
              <p className="text-xs text-neutral-600 -mt-1 font-medium">Detailing</p>
            </div>
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <motion.a
                key={item.name}
                href={item.href}
                whileHover={{ y: -2 }}
                className="text-neutral-700 hover:text-blue-600 transition-colors duration-200 font-medium"
              >
                {item.name}
              </motion.a>
            ))}
          </div>

          {/* Contact Info & CTA */}
          <div className="hidden lg:flex items-center space-x-4">
            <div className="flex items-center space-x-4 text-sm text-neutral-600">
              <div className="flex items-center space-x-1">
                <Phone className="w-4 h-4" />
                <span>(*************</span>
              </div>
              <div className="flex items-center space-x-1">
                <MapPin className="w-4 h-4" />
                <span>Four Corners Area</span>
              </div>
            </div>
            
            <HoverBorderGradient
              containerClassName="rounded-full"
              className="bg-transparent text-neutral-800 px-6 py-2 text-sm font-semibold"
            >
              Get Quote
            </HoverBorderGradient>
          </div>

          {/* Mobile Menu Button */}
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={() => setIsOpen(!isOpen)}
            className="md:hidden p-2 rounded-lg bg-neutral-100 text-neutral-700"
          >
            {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </motion.button>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="md:hidden bg-white/95 backdrop-blur-md border-t border-neutral-200/50"
          >
            <div className="container mx-auto px-4 py-4">
              <div className="flex flex-col space-y-4">
                {navItems.map((item, index) => (
                  <motion.a
                    key={item.name}
                    href={item.href}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    onClick={() => setIsOpen(false)}
                    className="text-neutral-700 hover:text-blue-600 transition-colors duration-200 font-medium py-2"
                  >
                    {item.name}
                  </motion.a>
                ))}
                
                <div className="pt-4 border-t border-neutral-200">
                  <div className="flex flex-col space-y-2 text-sm text-neutral-600 mb-4">
                    <div className="flex items-center space-x-2">
                      <Phone className="w-4 h-4" />
                      <span>(*************</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4" />
                      <span>Four Corners Area</span>
                    </div>
                  </div>
                  
                  <HoverBorderGradient
                    containerClassName="rounded-full w-full"
                    className="bg-transparent text-neutral-800 px-6 py-3 text-sm font-semibold w-full text-center"
                  >
                    Get Free Quote
                  </HoverBorderGradient>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.nav>
  );
}
