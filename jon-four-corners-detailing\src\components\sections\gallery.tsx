"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { scrollFadeIn } from "@/lib/utils";
import { ChevronLeft, ChevronRight, Eye } from "lucide-react";

export function Gallery() {
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedImage, setSelectedImage] = useState<number | null>(null);

  const categories = [
    { id: "all", name: "All Work" },
    { id: "ceramic", name: "Ceramic Coating" },
    { id: "correction", name: "Paint Correction" },
    { id: "interior", name: "Interior Detail" },
    { id: "luxury", name: "Luxury Vehicles" },
  ];

  const galleryItems = [
    {
      id: 1,
      category: "ceramic",
      title: "Tesla Model S - Ceramic Coating",
      before: "/api/placeholder/400/300",
      after: "/api/placeholder/400/300",
      description: "9H ceramic coating application with paint correction",
    },
    {
      id: 2,
      category: "correction",
      title: "BMW M3 - Paint Correction",
      before: "/api/placeholder/400/300",
      after: "/api/placeholder/400/300",
      description: "Multi-stage paint correction removing years of swirl marks",
    },
    {
      id: 3,
      category: "luxury",
      title: "Mercedes S-Class - Full Detail",
      before: "/api/placeholder/400/300",
      after: "/api/placeholder/400/300",
      description: "Complete exterior and interior restoration",
    },
    {
      id: 4,
      category: "interior",
      title: "Porsche 911 - Interior Detail",
      before: "/api/placeholder/400/300",
      after: "/api/placeholder/400/300",
      description: "Leather conditioning and fabric protection",
    },
    {
      id: 5,
      category: "ceramic",
      title: "Audi RS6 - Ceramic Protection",
      before: "/api/placeholder/400/300",
      after: "/api/placeholder/400/300",
      description: "Premium ceramic coating with 5-year warranty",
    },
    {
      id: 6,
      category: "correction",
      title: "Lamborghini Huracán - Paint Restoration",
      before: "/api/placeholder/400/300",
      after: "/api/placeholder/400/300",
      description: "Exotic car paint correction and protection",
    },
  ];

  const filteredItems = selectedCategory === "all" 
    ? galleryItems 
    : galleryItems.filter(item => item.category === selectedCategory);

  return (
    <section id="gallery" className="py-20 bg-gradient-to-b from-neutral-50 to-white relative overflow-hidden">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          {...scrollFadeIn}
          className="text-center mb-16"
        >
          <motion.span
            {...scrollFadeIn}
            className="inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-4"
          >
            Our Work
          </motion.span>
          
          <motion.h2
            {...scrollFadeIn}
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-800 mb-6"
          >
            Before & After
            <span className="bg-gradient-to-r from-blue-600 to-yellow-600 bg-clip-text text-transparent"> Gallery</span>
          </motion.h2>
          
          <motion.p
            {...scrollFadeIn}
            className="text-lg md:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed"
          >
            See the dramatic transformations we achieve through our meticulous four-corner approach. 
            Every vehicle tells a story of restoration and protection.
          </motion.p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          {...scrollFadeIn}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category) => (
            <motion.button
              key={category.id}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                selectedCategory === category.id
                  ? "bg-gradient-to-r from-blue-600 to-yellow-600 text-white shadow-lg"
                  : "bg-white text-neutral-700 border border-neutral-200 hover:border-blue-300"
              }`}
            >
              {category.name}
            </motion.button>
          ))}
        </motion.div>

        {/* Gallery Grid */}
        <motion.div
          layout
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          <AnimatePresence>
            {filteredItems.map((item, index) => (
              <motion.div
                key={item.id}
                layout
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white rounded-2xl overflow-hidden shadow-lg border border-neutral-100 group hover:shadow-xl transition-all duration-300"
              >
                {/* Before/After Images */}
                <div className="relative aspect-video overflow-hidden">
                  <div className="grid grid-cols-2 h-full">
                    {/* Before */}
                    <div className="relative bg-gradient-to-br from-red-100 to-orange-100 flex items-center justify-center">
                      <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold">
                        BEFORE
                      </div>
                      <div className="text-red-600 font-semibold opacity-50">
                        Before Image
                      </div>
                    </div>
                    
                    {/* After */}
                    <div className="relative bg-gradient-to-br from-blue-100 to-green-100 flex items-center justify-center">
                      <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold">
                        AFTER
                      </div>
                      <div className="text-green-600 font-semibold opacity-50">
                        After Image
                      </div>
                    </div>
                  </div>
                  
                  {/* Hover Overlay */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setSelectedImage(item.id)}
                      className="bg-white text-neutral-800 p-3 rounded-full shadow-lg"
                    >
                      <Eye className="w-6 h-6" />
                    </motion.button>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="font-bold text-neutral-800 mb-2 text-lg">
                    {item.title}
                  </h3>
                  <p className="text-neutral-600 text-sm leading-relaxed">
                    {item.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          {...scrollFadeIn}
          className="text-center mt-16"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-gradient-to-r from-blue-600 to-yellow-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300"
          >
            See More Work
          </motion.button>
        </motion.div>
      </div>

      {/* Modal for enlarged images would go here */}
      <AnimatePresence>
        {selectedImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedImage(null)}
          >
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.8 }}
              className="bg-white rounded-2xl p-4 max-w-4xl w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center">
                <h3 className="text-2xl font-bold mb-4">
                  {galleryItems.find(item => item.id === selectedImage)?.title}
                </h3>
                <div className="aspect-video bg-gradient-to-r from-neutral-100 to-neutral-200 rounded-xl flex items-center justify-center">
                  <span className="text-neutral-500">Enlarged Before/After View</span>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
}
