"use client";

import { motion } from "motion/react";
import { TextGenerateEffect } from "@/components/ui/text-generate-effect";
import { BackgroundGradient } from "@/components/ui/background-gradient";
import { HoverBorderGradient } from "@/components/ui/hover-border-gradient";
import { fadeInUp, staggerContainer } from "@/lib/utils";
import { ArrowRight, Star, Shield, Award } from "lucide-react";

export function Hero() {
  const heroText = "Automotive Artistry That Protects Your Investment";
  const subText = "Premium detailing services with meticulous attention to every corner of your vehicle. From ceramic coating to paint correction, we deliver results that exceed expectations.";

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-neutral-50 via-blue-50/30 to-yellow-50/20">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-20 left-10 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-yellow-400/20 rounded-full blur-3xl animate-float" style={{ animationDelay: "2s" }}></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          variants={staggerContainer}
          initial="initial"
          animate="animate"
          className="text-center max-w-6xl mx-auto"
        >
          {/* Trust Indicators */}
          <motion.div
            variants={fadeInUp}
            className="flex items-center justify-center gap-6 mb-8"
          >
            <div className="flex items-center gap-2 text-sm text-neutral-600">
              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
              <span>5.0 Rating</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-neutral-600">
              <Shield className="w-4 h-4 text-blue-600" />
              <span>Fully Insured</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-neutral-600">
              <Award className="w-4 h-4 text-yellow-600" />
              <span>Certified Professional</span>
            </div>
          </motion.div>

          {/* Main Heading */}
          <motion.div variants={fadeInUp} className="mb-8">
            <TextGenerateEffect
              words={heroText}
              className="text-4xl md:text-6xl lg:text-7xl font-bold text-center bg-gradient-to-r from-neutral-800 via-blue-800 to-neutral-800 bg-clip-text text-transparent"
            />
          </motion.div>

          {/* Subheading */}
          <motion.p
            variants={fadeInUp}
            className="text-lg md:text-xl lg:text-2xl text-neutral-600 max-w-4xl mx-auto mb-12 leading-relaxed"
          >
            {subText}
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            variants={fadeInUp}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
          >
            <BackgroundGradient className="rounded-full">
              <button className="bg-white text-neutral-800 px-8 py-4 rounded-full font-semibold text-lg flex items-center gap-2 hover:shadow-lg transition-all duration-300">
                Get Free Quote
                <ArrowRight className="w-5 h-5" />
              </button>
            </BackgroundGradient>
            
            <HoverBorderGradient
              containerClassName="rounded-full"
              className="bg-transparent text-neutral-800 px-8 py-4 font-semibold text-lg"
            >
              View Our Work
            </HoverBorderGradient>
          </motion.div>

          {/* Before/After Preview */}
          <motion.div
            variants={fadeInUp}
            className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto"
          >
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-red-500/20 to-orange-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
              <div className="relative bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-xl">
                <div className="aspect-video bg-gradient-to-br from-red-100 to-orange-100 rounded-xl mb-4 flex items-center justify-center">
                  <span className="text-red-600 font-semibold">BEFORE</span>
                </div>
                <h3 className="font-semibold text-neutral-800 mb-2">Dull & Oxidized</h3>
                <p className="text-sm text-neutral-600">Faded paint, water spots, and surface contamination</p>
              </div>
            </div>

            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-green-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
              <div className="relative bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-white/20 shadow-xl">
                <div className="aspect-video bg-gradient-to-br from-blue-100 to-green-100 rounded-xl mb-4 flex items-center justify-center">
                  <span className="text-blue-600 font-semibold">AFTER</span>
                </div>
                <h3 className="font-semibold text-neutral-800 mb-2">Showroom Shine</h3>
                <p className="text-sm text-neutral-600">Deep gloss, protected finish, and mirror-like clarity</p>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2, duration: 1 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div className="flex flex-col items-center gap-2 text-neutral-400">
          <span className="text-sm">Scroll to explore</span>
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-6 h-10 border-2 border-neutral-300 rounded-full flex justify-center"
          >
            <motion.div
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-1 h-3 bg-neutral-400 rounded-full mt-2"
            />
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
}
