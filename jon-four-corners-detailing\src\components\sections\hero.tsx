"use client";

import { motion } from "motion/react";
import { TextGenerateEffect } from "@/components/ui/text-generate-effect";
import { BackgroundGradient } from "@/components/ui/background-gradient";
import { HoverBorderGradient } from "@/components/ui/hover-border-gradient";
import { fadeInUp, staggerContainer } from "@/lib/utils";
import { ArrowRight, Star, Shield, Award } from "lucide-react";

export function Hero() {
  const heroText = "Automotive Artistry That Protects Your Investment";
  const subText = "Premium detailing services with meticulous attention to every corner of your vehicle. From ceramic coating to paint correction, we deliver results that exceed expectations.";

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden hero-background">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-blue-400/30 to-blue-600/20 rounded-full blur-3xl animate-float glow-effect"></div>
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-yellow-400/30 to-yellow-600/20 rounded-full blur-3xl animate-float glow-effect-accent" style={{ animationDelay: "2s" }}></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/5 to-yellow-500/5 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          variants={staggerContainer}
          initial="initial"
          animate="animate"
          className="text-center max-w-6xl mx-auto"
        >
          {/* Trust Indicators */}
          <motion.div
            variants={fadeInUp}
            className="flex items-center justify-center gap-6 mb-8"
          >
            <div className="flex items-center gap-2 text-sm text-neutral-600">
              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
              <span>5.0 Rating</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-neutral-600">
              <Shield className="w-4 h-4 text-blue-600" />
              <span>Fully Insured</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-neutral-600">
              <Award className="w-4 h-4 text-yellow-600" />
              <span>Certified Professional</span>
            </div>
          </motion.div>

          {/* Enhanced Main Heading */}
          <motion.div variants={fadeInUp} className="mb-8">
            <TextGenerateEffect
              words={heroText}
              className="text-4xl md:text-6xl lg:text-8xl font-bold text-center luxury-text-gradient leading-tight"
            />
          </motion.div>

          {/* Enhanced Subheading */}
          <motion.p
            variants={fadeInUp}
            className="text-lg md:text-xl lg:text-2xl text-neutral-700 max-w-4xl mx-auto mb-12 leading-relaxed font-medium"
          >
            {subText}
          </motion.p>

          {/* Enhanced CTA Buttons */}
          <motion.div
            variants={fadeInUp}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16"
          >
            <BackgroundGradient className="rounded-full">
              <button className="bg-white text-neutral-800 px-10 py-5 rounded-full font-bold text-lg flex items-center gap-3 luxury-hover magnetic-hover">
                Get Free Quote
                <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
              </button>
            </BackgroundGradient>

            <HoverBorderGradient
              containerClassName="rounded-full"
              className="bg-transparent text-neutral-800 px-10 py-5 font-bold text-lg magnetic-hover"
            >
              View Our Work
            </HoverBorderGradient>
          </motion.div>

          {/* Enhanced Before/After Preview */}
          <motion.div
            variants={fadeInUp}
            className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto"
          >
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-red-500/30 to-orange-500/30 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>
              <div className="relative glass-card rounded-3xl p-8 border border-white/30 luxury-shadow group-hover:luxury-shadow-lg transition-all duration-500">
                <div className="aspect-video bg-gradient-to-br from-red-100 to-orange-100 rounded-2xl mb-6 flex items-center justify-center relative overflow-hidden">
                  <div className="absolute top-3 left-3 bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                    BEFORE
                  </div>
                  <span className="text-red-600 font-semibold text-lg">Dull & Faded</span>
                </div>
                <h3 className="font-bold text-neutral-800 mb-3 text-xl">Dull & Oxidized</h3>
                <p className="text-neutral-600 leading-relaxed">Faded paint, water spots, and surface contamination diminish your vehicle's appearance</p>
              </div>
            </div>

            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/30 to-green-500/30 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>
              <div className="relative glass-card rounded-3xl p-8 border border-white/30 luxury-shadow group-hover:luxury-shadow-lg transition-all duration-500">
                <div className="aspect-video bg-gradient-to-br from-blue-100 to-green-100 rounded-2xl mb-6 flex items-center justify-center relative overflow-hidden">
                  <div className="absolute top-3 right-3 bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                    AFTER
                  </div>
                  <span className="text-green-600 font-semibold text-lg">Showroom Ready</span>
                </div>
                <h3 className="font-bold text-neutral-800 mb-3 text-xl">Showroom Shine</h3>
                <p className="text-neutral-600 leading-relaxed">Deep gloss, protected finish, and mirror-like clarity that exceeds expectations</p>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2, duration: 1 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div className="flex flex-col items-center gap-2 text-neutral-400">
          <span className="text-sm">Scroll to explore</span>
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-6 h-10 border-2 border-neutral-300 rounded-full flex justify-center"
          >
            <motion.div
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-1 h-3 bg-neutral-400 rounded-full mt-2"
            />
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
}
