"use client";

import { motion } from "motion/react";
import { HoverEffect } from "@/components/ui/card-hover-effect";
import { scrollFadeIn, createScrollFadeIn } from "@/lib/utils";
import { 
  Sparkles, 
  Shield, 
  Truck, 
  Paintbrush, 
  Droplets, 
  Car 
} from "lucide-react";

export function Services() {
  const services = [
    {
      title: "Ceramic Coating",
      description: "Advanced nano-ceramic protection that bonds to your paint, providing years of protection against UV rays, chemicals, and environmental contaminants.",
      icon: <Shield className="w-6 h-6 text-white" />,
    },
    {
      title: "Paint Correction",
      description: "Multi-stage polishing process to remove swirl marks, scratches, and oxidation, restoring your vehicle's paint to better-than-new condition.",
      icon: <Paintbrush className="w-6 h-6 text-white" />,
    },
    {
      title: "Mobile Detailing",
      description: "Professional detailing services brought directly to your location. Convenience without compromising on quality or attention to detail.",
      icon: <Truck className="w-6 h-6 text-white" />,
    },
    {
      title: "Interior Deep Clean",
      description: "Comprehensive interior restoration including leather conditioning, fabric protection, and steam cleaning for a fresh, pristine cabin.",
      icon: <Sparkles className="w-6 h-6 text-white" />,
    },
    {
      title: "Hydrophobic Treatment",
      description: "Advanced water-repelling technology that makes maintenance easier while providing superior protection against water damage and staining.",
      icon: <Droplets className="w-6 h-6 text-white" />,
    },
    {
      title: "Full Vehicle Restoration",
      description: "Complete transformation service combining all our expertise to bring neglected vehicles back to showroom condition and beyond.",
      icon: <Car className="w-6 h-6 text-white" />,
    },
  ];

  return (
    <section className="py-24 bg-gradient-to-b from-white via-neutral-50/50 to-white relative overflow-hidden">
      {/* Enhanced Background Elements */}
      <div className="absolute top-0 left-0 w-full h-2 luxury-gradient"></div>
      <div className="absolute top-10 right-10 w-64 h-64 bg-gradient-to-r from-blue-400/10 to-yellow-400/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-10 left-10 w-80 h-80 bg-gradient-to-r from-yellow-400/10 to-blue-400/10 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4">
        <motion.div
          {...scrollFadeIn}
          className="text-center mb-16"
        >
          <motion.span
            {...scrollFadeIn}
            className="inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-4"
          >
            Our Services
          </motion.span>
          
          <motion.h2
            {...scrollFadeIn}
            className="text-4xl md:text-5xl lg:text-7xl font-bold mb-6"
          >
            <span className="luxury-text-gradient">Four Corners of</span>
            <br />
            <span className="bg-gradient-to-r from-blue-600 to-yellow-600 bg-clip-text text-transparent">Excellence</span>
          </motion.h2>
          
          <motion.p
            {...scrollFadeIn}
            className="text-lg md:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed"
          >
            Every vehicle receives our comprehensive four-corner approach: meticulous preparation, 
            expert application, quality assurance, and ongoing protection. No detail overlooked, 
            no corner cut.
          </motion.p>
        </motion.div>

        <motion.div
          {...scrollFadeIn}
          className="mb-16"
        >
          <HoverEffect items={services} className="max-w-6xl mx-auto" />
        </motion.div>

        {/* Enhanced Process Overview */}
        <motion.div
          {...scrollFadeIn}
          className="glass-card rounded-3xl p-8 md:p-12 max-w-5xl mx-auto luxury-shadow-lg border border-white/30"
        >
          <h3 className="text-3xl md:text-4xl font-bold text-center mb-12">
            <span className="luxury-text-gradient">Our Four-Corner Process</span>
          </h3>

          <div className="grid md:grid-cols-4 gap-8">
            {[
              { number: "01", title: "Assessment", description: "Thorough inspection and consultation", icon: "🔍" },
              { number: "02", title: "Preparation", description: "Meticulous cleaning and surface prep", icon: "🧽" },
              { number: "03", title: "Application", description: "Expert technique and premium products", icon: "✨" },
              { number: "04", title: "Protection", description: "Final inspection and care instructions", icon: "🛡️" },
            ].map((step, index) => (
              <motion.div
                key={index}
                {...createScrollFadeIn(index * 0.1)}
                className="text-center group"
              >
                <div className="relative mb-6">
                  <div className="w-20 h-20 luxury-gradient rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto luxury-shadow group-hover:luxury-shadow-lg transition-all duration-300 group-hover:scale-105">
                    {step.number}
                  </div>
                  <div className="absolute -top-2 -right-2 text-2xl">
                    {step.icon}
                  </div>
                </div>
                <h4 className="font-bold text-neutral-800 mb-3 text-lg">{step.title}</h4>
                <p className="text-neutral-600 leading-relaxed">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
