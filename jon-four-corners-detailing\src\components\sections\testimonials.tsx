"use client";

import { motion } from "motion/react";
import { scrollFadeIn, createScrollFadeIn } from "@/lib/utils";
import { Star, Quote } from "lucide-react";

export function Testimonials() {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Tesla Model S Owner",
      content: "<PERSON>'s attention to detail is absolutely incredible. My Tesla looks better than the day I bought it. The ceramic coating has been a game-changer for maintenance.",
      rating: 5,
      image: "/api/placeholder/60/60",
    },
    {
      name: "<PERSON>",
      role: "BMW M3 Enthusiast",
      content: "I've tried many detailing services, but <PERSON>'s four-corner approach is unmatched. The paint correction work was flawless, and the results speak for themselves.",
      rating: 5,
      image: "/api/placeholder/60/60",
    },
    {
      name: "<PERSON>",
      role: "Luxury Car Collector",
      content: "Professional, reliable, and the quality is consistently outstanding. <PERSON> treats every vehicle like it's his own. Highly recommend for anyone who values their investment.",
      rating: 5,
      image: "/api/placeholder/60/60",
    },
    {
      name: "<PERSON>",
      role: "Mercedes-Benz Owner",
      content: "The mobile service is incredibly convenient, and the results are always perfect. <PERSON>'s expertise and premium products make all the difference.",
      rating: 5,
      image: "/api/placeholder/60/60",
    },
  ];

  const stats = [
    { number: "500+", label: "Vehicles Detailed" },
    { number: "5.0", label: "Average Rating" },
    { number: "100%", label: "Customer Satisfaction" },
    { number: "3+", label: "Years Experience" },
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-white to-neutral-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          {...scrollFadeIn}
          className="text-center mb-16"
        >
          <motion.span
            {...scrollFadeIn}
            className="inline-block px-4 py-2 bg-yellow-100 text-yellow-800 rounded-full text-sm font-semibold mb-4"
          >
            Client Testimonials
          </motion.span>
          
          <motion.h2
            {...scrollFadeIn}
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-800 mb-6"
          >
            What Our Clients
            <span className="bg-gradient-to-r from-blue-600 to-yellow-600 bg-clip-text text-transparent"> Say</span>
          </motion.h2>
          
          <motion.p
            {...scrollFadeIn}
            className="text-lg md:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed"
          >
            Don't just take our word for it. Here's what vehicle owners across the Four Corners region 
            say about our premium detailing services.
          </motion.p>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          {...scrollFadeIn}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              {...createScrollFadeIn(index * 0.1)}
              className="text-center"
            >
              <div className="text-3xl md:text-4xl font-bold text-neutral-800 mb-2">
                {stat.number}
              </div>
              <div className="text-sm md:text-base text-neutral-600">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Testimonials Grid */}
        <motion.div
          {...scrollFadeIn}
          className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto"
        >
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              {...createScrollFadeIn(index * 0.1)}
              className="bg-white rounded-2xl p-8 shadow-lg border border-neutral-100 relative group hover:shadow-xl transition-all duration-300"
            >
              {/* Quote Icon */}
              <div className="absolute top-6 right-6 text-blue-200 group-hover:text-blue-300 transition-colors duration-300">
                <Quote className="w-8 h-8" />
              </div>

              {/* Rating */}
              <div className="flex items-center gap-1 mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                ))}
              </div>

              {/* Content */}
              <p className="text-neutral-700 leading-relaxed mb-6 text-lg">
                "{testimonial.content}"
              </p>

              {/* Author */}
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-yellow-500 rounded-full flex items-center justify-center text-white font-semibold">
                  {testimonial.name.split(' ').map(n => n[0]).join('')}
                </div>
                <div>
                  <div className="font-semibold text-neutral-800">
                    {testimonial.name}
                  </div>
                  <div className="text-sm text-neutral-600">
                    {testimonial.role}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          {...scrollFadeIn}
          className="text-center mt-16"
        >
          <motion.div
            {...scrollFadeIn}
            className="bg-gradient-to-r from-blue-50 to-yellow-50 rounded-3xl p-8 md:p-12 max-w-4xl mx-auto"
          >
            <h3 className="text-2xl md:text-3xl font-bold text-neutral-800 mb-4">
              Ready to Join Our Satisfied Clients?
            </h3>
            <p className="text-lg text-neutral-600 mb-8 max-w-2xl mx-auto">
              Experience the difference that professional, artisan-level detailing can make for your vehicle. 
              Get your free quote today.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-blue-600 to-yellow-600 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Get Your Free Quote
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
